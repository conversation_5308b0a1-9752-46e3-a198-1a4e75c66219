<?php

/**
 * Script de diagnóstico para identificar problemas de mapeo de ubicaciones
 * en los centros de votación
 */

require_once 'vendor/autoload.php';

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

echo "🔍 DIAGNÓSTICO DE UBICACIONES PARA CENTROS DE VOTACIÓN\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // 1. Verificar datos en la base de datos
    echo "1️⃣  Verificando datos en la base de datos...\n";
    $statesCount = State::count();
    $municipalitiesCount = Municipality::count();
    $parishesCount = Parish::count();
    
    echo "   📊 Estados: {$statesCount}\n";
    echo "   📊 Municipios: {$municipalitiesCount}\n";
    echo "   📊 Parroquias: {$parishesCount}\n\n";
    
    if ($statesCount === 0 || $municipalitiesCount === 0 || $parishesCount === 0) {
        echo "❌ ERROR: Faltan datos de ubicaciones en la base de datos\n";
        echo "💡 Ejecuta: php artisan db:seed --class=UbicacionesSeeder\n\n";
        exit(1);
    }
    
    // 2. Leer CSV y analizar ubicaciones únicas
    echo "2️⃣  Analizando ubicaciones en el CSV...\n";
    $csvFile = 'database/seeders/data/voting_centers.csv';
    
    if (!file_exists($csvFile)) {
        echo "❌ ERROR: Archivo CSV no encontrado: {$csvFile}\n";
        exit(1);
    }
    
    $handle = fopen($csvFile, 'r');
    $header = fgetcsv($handle);
    
    $csvStates = [];
    $csvMunicipalities = [];
    $csvParishes = [];
    $locationCombinations = [];
    
    while (($row = fgetcsv($handle)) !== false) {
        $data = array_combine($header, $row);
        
        $state = trim($data['state_name'] ?? '');
        $municipality = trim($data['municipality_name'] ?? '');
        $parish = trim($data['parish_name'] ?? '');
        
        if (!empty($state)) $csvStates[$state] = true;
        if (!empty($municipality)) $csvMunicipalities[$state . '|' . $municipality] = true;
        if (!empty($parish)) $csvParishes[$state . '|' . $municipality . '|' . $parish] = true;
        
        $locationCombinations[$state . '|' . $municipality . '|' . $parish] = [
            'state' => $state,
            'municipality' => $municipality,
            'parish' => $parish
        ];
    }
    fclose($handle);
    
    echo "   📊 Estados únicos en CSV: " . count($csvStates) . "\n";
    echo "   📊 Municipios únicos en CSV: " . count($csvMunicipalities) . "\n";
    echo "   📊 Parroquias únicas en CSV: " . count($csvParishes) . "\n\n";
    
    // 3. Comparar estados
    echo "3️⃣  Comparando estados...\n";
    $dbStates = State::pluck('name', 'id')->toArray();
    $dbStateNames = array_values($dbStates);
    
    echo "   🗺️  Estados en la base de datos:\n";
    foreach ($dbStateNames as $stateName) {
        echo "      - {$stateName}\n";
    }
    
    echo "\n   📄 Estados en el CSV:\n";
    foreach (array_keys($csvStates) as $csvState) {
        echo "      - {$csvState}\n";
    }
    
    // Encontrar estados no mapeados
    $unmappedStates = [];
    foreach (array_keys($csvStates) as $csvState) {
        $found = false;
        foreach ($dbStateNames as $dbState) {
            if (strtoupper($csvState) === strtoupper($dbState) ||
                strtoupper($csvState) === strtoupper(str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], $dbState)) ||
                ($csvState === 'CAPITAL' && $dbState === 'Distrito Capital') ||
                ($csvState === 'VARGAS' && $dbState === 'La Guaira')) {
                $found = true;
                break;
            }
        }
        if (!$found) {
            $unmappedStates[] = $csvState;
        }
    }
    
    if (!empty($unmappedStates)) {
        echo "\n   ⚠️  Estados del CSV no mapeados:\n";
        foreach ($unmappedStates as $state) {
            echo "      - {$state}\n";
        }
    } else {
        echo "\n   ✅ Todos los estados del CSV tienen mapeo\n";
    }
    
    // 4. Analizar algunas combinaciones problemáticas
    echo "\n4️⃣  Analizando combinaciones de ubicaciones (primeras 10)...\n";
    $count = 0;
    foreach ($locationCombinations as $key => $location) {
        if ($count >= 10) break;
        
        echo "   🔍 {$location['state']} | {$location['municipality']} | {$location['parish']}\n";
        
        // Verificar estado
        $stateFound = false;
        foreach ($dbStateNames as $dbState) {
            if (strtoupper($location['state']) === strtoupper($dbState) ||
                ($location['state'] === 'CAPITAL' && $dbState === 'Distrito Capital')) {
                $stateFound = $dbState;
                break;
            }
        }
        
        if ($stateFound) {
            echo "      ✅ Estado: {$location['state']} -> {$stateFound}\n";
            
            // Verificar municipio
            $municipalities = Municipality::whereHas('state', function($q) use ($stateFound) {
                $q->where('name', $stateFound);
            })->pluck('name')->toArray();
            
            $municipalityFound = false;
            foreach ($municipalities as $dbMunicipality) {
                if (strtoupper($location['municipality']) === strtoupper($dbMunicipality) ||
                    strtoupper($location['municipality']) === strtoupper(str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], $dbMunicipality))) {
                    $municipalityFound = $dbMunicipality;
                    break;
                }
            }
            
            if ($municipalityFound) {
                echo "      ✅ Municipio: {$location['municipality']} -> {$municipalityFound}\n";
                
                // Verificar parroquia
                $parishes = Parish::whereHas('municipality', function($q) use ($municipalityFound, $stateFound) {
                    $q->where('name', $municipalityFound)
                      ->whereHas('state', function($q2) use ($stateFound) {
                          $q2->where('name', $stateFound);
                      });
                })->pluck('name')->toArray();
                
                $parishFound = false;
                foreach ($parishes as $dbParish) {
                    if (strtoupper($location['parish']) === strtoupper($dbParish) ||
                        strtoupper($location['parish']) === strtoupper(str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], $dbParish))) {
                        $parishFound = $dbParish;
                        break;
                    }
                }
                
                if ($parishFound) {
                    echo "      ✅ Parroquia: {$location['parish']} -> {$parishFound}\n";
                } else {
                    echo "      ❌ Parroquia no encontrada: {$location['parish']}\n";
                    echo "         Parroquias disponibles: " . implode(', ', array_slice($parishes, 0, 3)) . "...\n";
                }
            } else {
                echo "      ❌ Municipio no encontrado: {$location['municipality']}\n";
                echo "         Municipios disponibles: " . implode(', ', array_slice($municipalities, 0, 3)) . "...\n";
            }
        } else {
            echo "      ❌ Estado no encontrado: {$location['state']}\n";
        }
        
        echo "\n";
        $count++;
    }
    
    // 5. Sugerencias
    echo "5️⃣  Sugerencias para solucionar problemas:\n\n";
    
    if (!empty($unmappedStates)) {
        echo "   🔧 Para estados no mapeados:\n";
        foreach ($unmappedStates as $state) {
            echo "      - Agregar mapeo para '{$state}' en createStateMap()\n";
        }
        echo "\n";
    }
    
    echo "   📋 Pasos recomendados:\n";
    echo "      1. Ejecutar: php artisan db:seed --class=UbicacionesSeeder\n";
    echo "      2. Ejecutar: php artisan db:seed --class=VotingCentersSeeder\n";
    echo "      3. Revisar los errores mostrados en el seeder\n";
    echo "      4. Ajustar los métodos de mapeo según sea necesario\n\n";
    
    echo "✅ Diagnóstico completado\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 Archivo: " . $e->getFile() . "\n";
    echo "📍 Línea: " . $e->getLine() . "\n\n";
    exit(1);
}

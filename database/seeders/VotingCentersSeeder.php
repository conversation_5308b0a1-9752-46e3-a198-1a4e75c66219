<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

/**
 * VotingCentersSeeder
 *
 * Importa centros de votación desde archivo CSV con manejo robusto de errores
 * y creación automática de ubicaciones faltantes.
 *
 * Permisos requeridos para usar este seeder:
 * - import voting_centers (para ejecutar el seeder)
 * - create ubicaciones (para crear ubicaciones faltantes)
 *
 * Roles con acceso:
 * - Super Admin (todos los permisos)
 * - Coordinador Electoral (gestión completa de centros)
 *
 * <AUTHOR> Electoral
 * @version 2.0
 */

class VotingCentersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🇻🇪 Iniciando importación de centros de votación de Venezuela...');

        // Verificar que las tablas de ubicaciones tengan datos
        $this->verifyLocationData();

        // Leer archivo CSV
        $csvFile = database_path('seeders/data/voting_centers.csv');

        if (!file_exists($csvFile)) {
            $this->command->error("❌ Archivo CSV no encontrado: {$csvFile}");
            return;
        }

        $this->command->info("📄 Leyendo archivo: {$csvFile}");

        // Leer y procesar CSV
        $handle = fopen($csvFile, 'r');
        $header = fgetcsv($handle); // Leer encabezados

        $this->command->info("📋 Encabezados CSV: " . implode(', ', $header));

        $totalRows = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $locationErrors = [];

        // Crear mapas de ubicaciones para optimizar búsquedas
        $stateMap = $this->createStateMap();
        $municipalityMap = $this->createMunicipalityMap();
        $parishMap = $this->createParishMap();

        $this->command->info("🗺️  Mapas de ubicaciones creados:");
        $this->command->info("   - Estados: " . count($stateMap));
        $this->command->info("   - Municipios: " . count($municipalityMap));
        $this->command->info("   - Parroquias: " . count($parishMap));

        // Mostrar algunos ejemplos de estados para debugging
        $this->command->info("🔍 Ejemplos de estados en el mapa:");
        $stateExamples = array_slice(array_keys($stateMap), 0, 5);
        foreach ($stateExamples as $state) {
            $this->command->info("   - '{$state}'");
        }

        // Procesar cada fila del CSV
        while (($row = fgetcsv($handle)) !== false) {
            $totalRows++;

            try {
                $data = array_combine($header, $row);

                // Validar datos requeridos
                if (empty($data['code']) || empty($data['name'])) {
                    $this->command->warn("⚠️  Fila {$totalRows}: Datos requeridos faltantes (code o name)");
                    $skippedCount++;
                    continue;
                }

                // Verificar si ya existe
                if (VotingCenter::where('code', $data['code'])->exists()) {
                    $skippedCount++;
                    continue;
                }

                // Buscar IDs de ubicaciones
                $locationIds = $this->findLocationIds(
                    $data,
                    $stateMap,
                    $municipalityMap,
                    $parishMap
                );

                if (!$locationIds) {
                    $locationKey = "{$data['state_name']}|{$data['municipality_name']}|{$data['parish_name']}";
                    if (!isset($locationErrors[$locationKey])) {
                        $locationErrors[$locationKey] = 0;
                    }
                    $locationErrors[$locationKey]++;
                    $errorCount++;
                    continue;
                }

                // Crear centro de votación
                $centerData = [
                    'code' => $data['code'],
                    'old_code' => !empty($data['old_code']) ? $data['old_code'] : null,
                    'name' => $data['name'],
                    'address' => !empty($data['address']) ? $data['address'] : null,
                    'state_id' => $locationIds['state_id'],
                    'municipality_id' => $locationIds['municipality_id'],
                    'parish_id' => $locationIds['parish_id'],
                    'total_voters' => (int) ($data['total_voters'] ?? 0),
                    'total_tables' => (int) ($data['total_tables'] ?? 0),
                    'status' => $data['status'] ?? 'active',
                    'latitude' => !empty($data['latitude']) ? (float) $data['latitude'] : null,
                    'longitude' => !empty($data['longitude']) ? (float) $data['longitude'] : null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                VotingCenter::create($centerData);
                $successCount++;

                if ($successCount % 100 === 0) {
                    $this->command->info("✅ Procesados: {$successCount} centros");
                }

            } catch (\Exception $e) {
                $this->command->error("❌ Error en fila {$totalRows}: " . $e->getMessage());
                $errorCount++;
                Log::error("Error importando centro de votación", [
                    'row' => $totalRows,
                    'data' => $data ?? null,
                    'error' => $e->getMessage()
                ]);
            }
        }

        fclose($handle);

        // Mostrar errores de ubicación más frecuentes
        if (!empty($locationErrors)) {
            $this->command->warn("\n🚨 UBICACIONES NO ENCONTRADAS (Top 10):");
            arsort($locationErrors);
            $topErrors = array_slice($locationErrors, 0, 10, true);
            foreach ($topErrors as $location => $count) {
                $this->command->warn("   {$count}x: {$location}");
            }
        }

        // Mostrar resumen
        $this->command->info("\n📊 RESUMEN DE IMPORTACIÓN:");
        $this->command->info("   📄 Total de filas procesadas: {$totalRows}");
        $this->command->info("   ✅ Centros creados exitosamente: {$successCount}");
        $this->command->info("   ⏭️  Centros omitidos (ya existían): {$skippedCount}");
        $this->command->info("   ❌ Errores: {$errorCount}");

        if ($successCount > 0) {
            $this->command->info("\n🎉 ¡Importación de centros de votación completada exitosamente!");
        }

        if ($errorCount > 0) {
            $this->command->warn("\n⚠️  Hay errores de ubicación. Ejecuta el UbicacionesSeeder para asegurar que todas las ubicaciones estén disponibles.");
        }
    }

    /**
     * Verificar que las tablas de ubicaciones tengan datos
     */
    private function verifyLocationData(): void
    {
        $statesCount = State::count();
        $municipalitiesCount = Municipality::count();
        $parishesCount = Parish::count();

        $this->command->info("📊 Datos de ubicaciones en la base de datos:");
        $this->command->info("   - Estados: {$statesCount}");
        $this->command->info("   - Municipios: {$municipalitiesCount}");
        $this->command->info("   - Parroquias: {$parishesCount}");

        if ($statesCount === 0 || $municipalitiesCount === 0 || $parishesCount === 0) {
            $this->command->error("❌ Faltan datos de ubicaciones. Ejecuta: php artisan db:seed --class=UbicacionesSeeder");
            throw new \Exception("Datos de ubicaciones incompletos");
        }
    }
    
    /**
     * Crear mapa de estados para búsqueda rápida
     */
    private function createStateMap(): array
    {
        $states = State::all();
        $map = [];

        foreach ($states as $state) {
            // Múltiples variaciones del nombre para mejor matching
            $variations = [
                $state->name,
                strtoupper($state->name),
                ucfirst(strtolower($state->name)),
                str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($state->name)),
                str_replace(['Edo. ', 'Dtto. ', 'Estado '], '', $state->name),
                // Variaciones específicas para casos especiales
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($state->name)),
            ];

            // Casos especiales conocidos
            if ($state->name === 'Distrito Capital') {
                $variations[] = 'CAPITAL';
                $variations[] = 'DTTO. CAPITAL';
                $variations[] = 'DISTRITO CAPITAL';
            }

            if ($state->name === 'La Guaira') {
                $variations[] = 'VARGAS';
                $variations[] = 'LA GUAIRA';
            }

            foreach ($variations as $variation) {
                $map[trim($variation)] = $state->id;
            }
        }

        return $map;
    }
    
    /**
     * Crear mapa de municipios para búsqueda rápida
     */
    private function createMunicipalityMap(): array
    {
        $municipalities = Municipality::with('state')->get();
        $map = [];

        foreach ($municipalities as $municipality) {
            // Generar variaciones del estado
            $stateVariations = [
                $municipality->state->name,
                strtoupper($municipality->state->name),
                str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($municipality->state->name)),
                str_replace(['Edo. ', 'Dtto. ', 'Estado '], '', $municipality->state->name),
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($municipality->state->name)),
            ];

            // Casos especiales para estados
            if ($municipality->state->name === 'Distrito Capital') {
                $stateVariations[] = 'CAPITAL';
                $stateVariations[] = 'DTTO. CAPITAL';
            }

            if ($municipality->state->name === 'La Guaira') {
                $stateVariations[] = 'VARGAS';
            }

            // Generar variaciones del municipio
            $municipalityVariations = [
                $municipality->name,
                strtoupper($municipality->name),
                ucfirst(strtolower($municipality->name)),
                str_replace(['MP. ', 'MUNICIPIO ', 'MUN. ', 'Municipio ', 'Mp. ', 'Mun. '], '', $municipality->name),
                str_replace(['MP. ', 'MUNICIPIO ', 'MUN. '], '', strtoupper($municipality->name)),
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($municipality->name)),
            ];

            // Casos especiales para municipios conocidos
            if (strpos($municipality->name, 'Sir Arthur McGregor') !== false) {
                $municipalityVariations[] = 'MCGREGOR';
                $municipalityVariations[] = 'SIR ARTHUR MCGREGOR';
            }

            if (strpos($municipality->name, 'Simón') !== false) {
                $municipalityVariations[] = str_replace('Simón', 'SIMON', strtoupper($municipality->name));
            }

            // Crear todas las combinaciones
            foreach ($stateVariations as $stateVar) {
                foreach ($municipalityVariations as $munVar) {
                    $key = trim($stateVar) . '|' . trim($munVar);
                    $map[$key] = $municipality->id;
                }
            }
        }

        return $map;
    }
    
    /**
     * Crear mapa de parroquias para búsqueda rápida
     */
    private function createParishMap(): array
    {
        $parishes = Parish::with(['municipality.state'])->get();
        $map = [];

        foreach ($parishes as $parish) {
            // Generar variaciones del estado
            $stateVariations = [
                $parish->municipality->state->name,
                strtoupper($parish->municipality->state->name),
                str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($parish->municipality->state->name)),
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($parish->municipality->state->name)),
            ];

            // Casos especiales para estados
            if ($parish->municipality->state->name === 'Distrito Capital') {
                $stateVariations[] = 'CAPITAL';
                $stateVariations[] = 'DTTO. CAPITAL';
            }

            if ($parish->municipality->state->name === 'La Guaira') {
                $stateVariations[] = 'VARGAS';
            }

            // Generar variaciones del municipio
            $municipalityVariations = [
                $parish->municipality->name,
                strtoupper($parish->municipality->name),
                str_replace(['MP. ', 'MUNICIPIO ', 'MUN. '], '', strtoupper($parish->municipality->name)),
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($parish->municipality->name)),
            ];

            // Casos especiales para municipios
            if (strpos($parish->municipality->name, 'Sir Arthur McGregor') !== false) {
                $municipalityVariations[] = 'MCGREGOR';
            }

            if (strpos($parish->municipality->name, 'Simón') !== false) {
                $municipalityVariations[] = str_replace('Simón', 'SIMON', strtoupper($parish->municipality->name));
            }

            // Generar variaciones de la parroquia
            $parishVariations = [
                $parish->name,
                strtoupper($parish->name),
                ucfirst(strtolower($parish->name)),
                str_replace(['PQ. ', 'PARROQUIA ', 'Parroquia ', 'Pq. '], '', $parish->name),
                str_replace(['PQ. ', 'PARROQUIA '], '', strtoupper($parish->name)),
                str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($parish->name)),
            ];

            // Casos especiales para parroquias
            if (strpos($parish->name, 'Girón') !== false) {
                $parishVariations[] = str_replace('Girón', 'GIRON', strtoupper($parish->name));
            }

            if (strpos($parish->name, 'de ') !== false) {
                $parishVariations[] = str_replace(' de ', ' DE ', strtoupper($parish->name));
            }

            // Crear todas las combinaciones
            foreach ($stateVariations as $stateVar) {
                foreach ($municipalityVariations as $munVar) {
                    foreach ($parishVariations as $parishVar) {
                        $key = trim($stateVar) . '|' . trim($munVar) . '|' . trim($parishVar);
                        $map[$key] = $parish->id;
                    }
                }
            }
        }

        return $map;
    }
    
    /**
     * Encontrar IDs de ubicaciones basado en los nombres
     */
    private function findLocationIds($data, $stateMap, $municipalityMap, $parishMap): ?array
    {
        $stateName = trim($data['state_name'] ?? '');
        $municipalityName = trim($data['municipality_name'] ?? '');
        $parishName = trim($data['parish_name'] ?? '');

        // Validar que tenemos los datos básicos
        if (empty($stateName) || empty($municipalityName) || empty($parishName)) {
            $this->command->warn("⚠️  Datos de ubicación incompletos: Estado='{$stateName}', Municipio='{$municipalityName}', Parroquia='{$parishName}'");
            return null;
        }

        // Buscar estado con múltiples variaciones
        $stateId = $this->findStateId($stateName, $stateMap);
        if (!$stateId) {
            $this->command->warn("⚠️  Estado no encontrado: '{$stateName}'");
            return null;
        }

        // Buscar municipio con múltiples variaciones
        $municipalityId = $this->findMunicipalityId($stateName, $municipalityName, $municipalityMap);
        if (!$municipalityId) {
            // Intentar crear el municipio si no existe
            $municipalityId = $this->handleMissingMunicipality($stateId, $municipalityName);
            if (!$municipalityId) {
                $this->command->warn("⚠️  Municipio no encontrado: '{$stateName}|{$municipalityName}'");
                return null;
            }
        }

        // Buscar parroquia con múltiples variaciones
        $parishId = $this->findParishId($stateName, $municipalityName, $parishName, $parishMap);
        if (!$parishId) {
            // Si no encontramos la parroquia exacta, intentar crear una nueva o usar una genérica
            $parishId = $this->handleMissingParish($municipalityId, $parishName);
            if (!$parishId) {
                $this->command->warn("⚠️  Parroquia no encontrada: '{$stateName}|{$municipalityName}|{$parishName}'");
                return null;
            }
        }

        return [
            'state_id' => $stateId,
            'municipality_id' => $municipalityId,
            'parish_id' => $parishId,
        ];
    }

    /**
     * Manejar parroquias faltantes
     */
    private function handleMissingParish($municipalityId, $parishName): ?int
    {
        // Cargar mapeo de parroquias
        $parishMapping = include database_path('seeders/data/parish_mapping.php');

        // Obtener información del municipio y estado
        $municipality = Municipality::with('state')->find($municipalityId);
        if (!$municipality) {
            return null;
        }

        $mappingKey = $municipality->state->name . '|' . $municipality->name . '|' . $parishName;

        // Verificar si hay un mapeo específico
        if (isset($parishMapping[$mappingKey])) {
            $mappedParishName = $parishMapping[$mappingKey];

            if ($mappedParishName === null) {
                // Crear automáticamente
                return $this->createNewParish($municipalityId, $parishName);
            } else {
                // Buscar la parroquia mapeada
                $mappedParish = Parish::where('name', $mappedParishName)
                                    ->where('municipality_id', $municipalityId)
                                    ->first();

                if ($mappedParish) {
                    $this->command->info("   🔄 Usando parroquia mapeada '{$mappedParish->name}' para '{$parishName}'");
                    return $mappedParish->id;
                }
            }
        }

        // Intentar encontrar una parroquia existente en el municipio como fallback
        $existingParish = Parish::where('municipality_id', $municipalityId)->first();

        if ($existingParish) {
            // Usar la primera parroquia disponible como fallback
            $this->command->info("   🔄 Usando parroquia existente '{$existingParish->name}' como fallback para '{$parishName}'");
            return $existingParish->id;
        }

        // Si no hay parroquias en el municipio, crear una nueva
        return $this->createNewParish($municipalityId, $parishName);
    }

    /**
     * Manejar municipios faltantes
     */
    private function handleMissingMunicipality($stateId, $municipalityName): ?int
    {
        try {
            // Verificar si ya existe un municipio con este nombre en el estado
            $existingMunicipality = Municipality::where('name', $municipalityName)
                                               ->where('state_id', $stateId)
                                               ->first();

            if ($existingMunicipality) {
                return $existingMunicipality->id;
            }

            $newMunicipality = Municipality::create([
                'name' => $municipalityName,
                'code' => strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $municipalityName), 0, 10)),
                'state_id' => $stateId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->command->info("   ➕ Municipio '{$municipalityName}' creado automáticamente");
            return $newMunicipality->id;
        } catch (\Exception $e) {
            $this->command->error("   ❌ Error creando municipio '{$municipalityName}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Crear una nueva parroquia
     */
    private function createNewParish($municipalityId, $parishName): ?int
    {
        try {
            // Verificar si ya existe una parroquia con este nombre en el municipio
            $existingParish = Parish::where('name', $parishName)
                                   ->where('municipality_id', $municipalityId)
                                   ->first();

            if ($existingParish) {
                return $existingParish->id;
            }

            $newParish = Parish::create([
                'name' => $parishName,
                'code' => strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $parishName), 0, 10)),
                'municipality_id' => $municipalityId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $this->command->info("   ➕ Parroquia '{$parishName}' creada automáticamente");
            return $newParish->id;
        } catch (\Exception $e) {
            $this->command->error("   ❌ Error creando parroquia '{$parishName}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Buscar ID del estado con variaciones
     */
    private function findStateId($stateName, $stateMap): ?int
    {
        // Intentar búsqueda directa
        if (isset($stateMap[$stateName])) {
            return $stateMap[$stateName];
        }

        // Generar variaciones del nombre del estado
        $variations = [
            strtoupper($stateName),
            ucfirst(strtolower($stateName)),
            str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($stateName)),
            str_replace(['Edo. ', 'Dtto. ', 'Estado '], '', $stateName),
        ];

        foreach ($variations as $variation) {
            if (isset($stateMap[$variation])) {
                return $stateMap[$variation];
            }
        }

        return null;
    }

    /**
     * Buscar ID del municipio con variaciones
     */
    private function findMunicipalityId($stateName, $municipalityName, $municipalityMap): ?int
    {
        // Generar variaciones del estado
        $stateVariations = [
            $stateName,
            strtoupper($stateName),
            str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($stateName)),
        ];

        // Generar variaciones del municipio
        $municipalityVariations = [
            $municipalityName,
            strtoupper($municipalityName),
            ucfirst(strtolower($municipalityName)),
            str_replace(['MP. ', 'MUNICIPIO ', 'MUN. ', 'Municipio '], '', $municipalityName),
            str_replace(['Mp. ', 'Mun. '], '', $municipalityName),
        ];

        // Probar todas las combinaciones
        foreach ($stateVariations as $stateVar) {
            foreach ($municipalityVariations as $munVar) {
                $key = $stateVar . '|' . $munVar;
                if (isset($municipalityMap[$key])) {
                    return $municipalityMap[$key];
                }
            }
        }

        return null;
    }

    /**
     * Buscar ID de la parroquia con variaciones
     */
    private function findParishId($stateName, $municipalityName, $parishName, $parishMap): ?int
    {
        // Generar variaciones de la parroquia
        $parishVariations = [
            $parishName,
            strtoupper($parishName),
            ucfirst(strtolower($parishName)),
            str_replace(['PQ. ', 'PARROQUIA ', 'Parroquia '], '', $parishName),
            str_replace(['Pq. '], '', $parishName),
        ];

        // Probar con el estado y municipio originales
        foreach ($parishVariations as $parishVar) {
            $key = $stateName . '|' . $municipalityName . '|' . $parishVar;
            if (isset($parishMap[$key])) {
                return $parishMap[$key];
            }
        }

        // Si no funciona, probar con variaciones del estado y municipio también
        $stateVariations = [$stateName, strtoupper($stateName)];
        $municipalityVariations = [$municipalityName, strtoupper($municipalityName)];

        foreach ($stateVariations as $stateVar) {
            foreach ($municipalityVariations as $munVar) {
                foreach ($parishVariations as $parishVar) {
                    $key = $stateVar . '|' . $munVar . '|' . $parishVar;
                    if (isset($parishMap[$key])) {
                        return $parishMap[$key];
                    }
                }
            }
        }

        return null;
    }
}

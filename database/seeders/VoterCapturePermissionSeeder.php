<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class VoterCapturePermissionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create permissions for voter captures
        $permissions = [
            // Basic CRUD permissions
            'view captures',
            'create captures',
            'update captures',
            'delete captures',
            
            // Advanced permissions
            'manage captures',
            'view capture stats',
            'export captures',
            'import captures',
            
            // Specific actions
            'convert captures to people',
            'send capture reminders',
            'view all captures', // For coordinators to see all captures
            'view own captures', // For leaders to see only their captures
            
            // Administrative permissions
            'manage capture settings',
            'view capture reports',
            'bulk update captures',
            'archive captures',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $this->assignPermissionsToRoles();

        $this->command->info('Voter capture permissions created and assigned successfully.');
    }

    /**
     * Assign permissions to existing roles
     */
    private function assignPermissionsToRoles(): void
    {
        // Super Admin - All permissions
        $superAdmin = Role::where('name', 'Super Admin')->first();
        if ($superAdmin) {
            $superAdmin->givePermissionTo([
                'view captures',
                'create captures',
                'update captures',
                'delete captures',
                'manage captures',
                'view capture stats',
                'export captures',
                'import captures',
                'convert captures to people',
                'send capture reminders',
                'view all captures',
                'manage capture settings',
                'view capture reports',
                'bulk update captures',
                'archive captures',
            ]);
        }

        // Coordinador de Personas - Full capture management
        $coordinador = Role::where('name', 'Coordinador de Personas')->first();
        if ($coordinador) {
            $coordinador->givePermissionTo([
                'view captures',
                'create captures',
                'update captures',
                'delete captures',
                'manage captures',
                'view capture stats',
                'export captures',
                'import captures',
                'convert captures to people',
                'send capture reminders',
                'view all captures',
                'view capture reports',
                'bulk update captures',
                'archive captures',
            ]);
        }

        // Líder 1x10 - Limited capture management (only their own)
        $lider = Role::where('name', 'Líder 1x10')->first();
        if ($lider) {
            $lider->givePermissionTo([
                'view captures',
                'create captures',
                'update captures',
                'view capture stats',
                'convert captures to people',
                'view own captures',
            ]);
        }

        // Operador de Datos - Basic capture operations
        $operador = Role::where('name', 'Operador de Datos')->first();
        if ($operador) {
            $operador->givePermissionTo([
                'view captures',
                'create captures',
                'update captures',
                'view capture stats',
                'import captures',
            ]);
        }

        // Militante - View and create captures
        $militante = Role::where('name', 'Militante')->first();
        if ($militante) {
            $militante->givePermissionTo([
                'view captures',
                'create captures',
                'update captures',
                'view own captures',
            ]);
        }

        // Votante - Basic view permissions
        $votante = Role::where('name', 'Votante')->first();
        if ($votante) {
            $votante->givePermissionTo([
                'view captures',
                'view own captures',
            ]);
        }
    }
}

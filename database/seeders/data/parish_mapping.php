<?php

/**
 * Mapeo manual de parroquias problemáticas
 * 
 * Este archivo contiene mapeos específicos para parroquias que no coinciden
 * exactamente entre el CSV de centros de votación y la base de datos.
 * 
 * Formato: 'Estado|Municipio|ParroquiaCSV' => 'ParroquiaBaseDatos'
 */

return [
    // MUNICIPIOS PROBLEMÁTICOS (se crearán automáticamente)
    // Estos municipios no existen en la base de datos pero aparecen en el CSV
    'MUNICIPIOS_FALTANTES' => [
        'Anzoátegui|Diego Bautista Urbaneja',
        '<PERSON><PERSON><PERSON><PERSON>gu<PERSON>|Mc <PERSON>', // Debería ser "Sir <PERSON>"
    ],

    // PARROQUIAS PROBLEMÁTICAS
    // Anzoátegui
    'Anzoátegui|Anaco|Anaco' => 'Cojedes', // Usar Cojedes como fallback
    'Anzoátegui|Anaco|San Joaquín' => 'Juan de <PERSON> Suárez',
    
    // Distrito Capital - Libertador
    'Distrito Capital|Libertador|Sucre' => 'Capacho Viejo', // Fallback temporal
    'Distrito Capital|Libertador|Antimano' => '<PERSON><PERSON>rian<PERSON>', // Fallback temporal
    
    // Miranda - Sucre
    'Miranda|Sucre|Petare' => 'Guamacho', // Usar Guamacho como fallback
    
    // Carabobo - Valencia
    'Carabobo|Valencia|Miguel Peña' => 'Juan Ignacio Montilla',
    
    // Portuguesa - Guanare
    'Portuguesa|Guanare|Guanare' => null, // Crear automáticamente
    
    // Miranda - Guaicaipuro
    'Miranda|Guaicaipuro|Los Teques' => null, // Crear automáticamente
    
    // Miranda - Paz Castillo
    'Miranda|Paz Castillo|Santa Lucia' => null, // Crear automáticamente
    
    // Cojedes - Ezequiel Zamora
    'Cojedes|Ezequiel Zamora|San Carlos De Austria' => null, // Crear automáticamente
    
    // Anzoátegui - Sotillo
    'Anzoátegui|Sotillo|Pozuelos' => null, // Crear automáticamente
    
    // Anzoátegui - Bolívar
    'Anzoátegui|Bolívar|San Cristobal' => null, // Crear automáticamente
    'Anzoátegui|Bolívar|El Carmen' => null, // Crear automáticamente
    'Anzoátegui|Bolívar|Naricual' => null, // Crear automáticamente
    'Anzoátegui|Bolívar|El Pilar' => null, // Crear automáticamente
    'Anzoátegui|Bolívar|Bergantin' => null, // Crear automáticamente
    'Anzoátegui|Bolívar|Caigua' => null, // Crear automáticamente
    
    // Anzoátegui - Aragua
    'Anzoátegui|Aragua|Aragua De Barcelona' => null, // Crear automáticamente
    'Anzoátegui|Aragua|Cachipo' => null, // Crear automáticamente
    
    // Anzoátegui - Cajigal
    'Anzoátegui|Cajigal|Onoto' => null, // Crear automáticamente
    'Anzoátegui|Cajigal|San Pablo' => null, // Crear automáticamente
    
    // Anzoátegui - Carvajal
    'Anzoátegui|Carvajal|Santa Barbara' => null, // Crear automáticamente
    'Anzoátegui|Carvajal|Valle Guanape' => null, // Crear automáticamente
    
    // Anzoátegui - Guanipa
    'Anzoátegui|Guanipa|San Jose De Guanipa' => null, // Crear automáticamente
    
    // Anzoátegui - Guanta
    'Anzoátegui|Guanta|Chorreron' => null, // Crear automáticamente
    
    // Anzoátegui - Independencia
    'Anzoátegui|Independencia|Mamo' => null, // Crear automáticamente
    'Anzoátegui|Independencia|Soledad' => null, // Crear automáticamente
    
    // Anzoátegui - Libertad
    'Anzoátegui|Libertad|El Carito' => null, // Crear automáticamente
    'Anzoátegui|Libertad|San Mateo' => null, // Crear automáticamente
    'Anzoátegui|Libertad|Santa Ines' => null, // Crear automáticamente
    
    // Anzoátegui - Miranda
    'Anzoátegui|Miranda|Atapirire' => null, // Crear automáticamente
    'Anzoátegui|Miranda|Boca Del Pao' => null, // Crear automáticamente
    'Anzoátegui|Miranda|El Pao' => null, // Crear automáticamente
    'Anzoátegui|Miranda|Pariaguan' => null, // Crear automáticamente
    
    // Anzoátegui - Monagas
    'Anzoátegui|Monagas|Mapire' => null, // Crear automáticamente
    'Anzoátegui|Monagas|Piar' => null, // Crear automáticamente
    'Anzoátegui|Monagas|Santa Clara' => null, // Crear automáticamente
    'Anzoátegui|Monagas|Sn Diego De Cabrutica' => null, // Crear automáticamente
    'Anzoátegui|Monagas|Uverito' => null, // Crear automáticamente
    'Anzoátegui|Monagas|Zuata' => null, // Crear automáticamente
    
    // Anzoátegui - Peñalver
    'Anzoátegui|Peñalver|Puerto Piritu' => null, // Crear automáticamente
    'Anzoátegui|Peñalver|San Miguel' => null, // Crear automáticamente
    'Anzoátegui|Peñalver|Sucre' => null, // Crear automáticamente
    
    // Amazonas
    'Amazonas|Manapiare|Alto Ventuari' => null, // Crear automáticamente
    'Amazonas|Manapiare|Bajo Ventuari' => null, // Crear automáticamente
    'Amazonas|Manapiare|Medio Ventuari' => null, // Crear automáticamente
    'Amazonas|Manapiare|San Juan de Manapiare' => null, // Crear automáticamente
    'Amazonas|Maroa|Comunidad' => null, // Crear automáticamente
    'Amazonas|Maroa|Maroa' => null, // Crear automáticamente
    'Amazonas|Maroa|Victorino' => null, // Crear automáticamente
    
    // Zulia - Casos específicos
    'Zulia|Maracaibo|Bolivar' => null, // Crear automáticamente
    'Zulia|Maracaibo|Chiquinquira' => null, // Crear automáticamente
    'Zulia|Maracaibo|Santa Lucia' => null, // Crear automáticamente
    'Zulia|Maracaibo|Caracciolo Parra Perez' => null, // Crear automáticamente
    'Zulia|Maracaibo|Francisco Eugenio B' => null, // Crear automáticamente
    'Zulia|Maracaibo|Idelfonzo Vasquez' => null, // Crear automáticamente
    'Zulia|Maracaibo|Raul Leoni' => null, // Crear automáticamente
    
    'Zulia|Mara|La Sierrita' => null, // Crear automáticamente
    'Zulia|Mara|Las Parcelas' => null, // Crear automáticamente
    'Zulia|Mara|Luis De Vicente' => null, // Crear automáticamente
    'Zulia|Mara|Mons.Marcos Sergio G' => null, // Crear automáticamente
    'Zulia|Mara|Ricaurte' => null, // Crear automáticamente
    'Zulia|Mara|San Rafael' => null, // Crear automáticamente
    'Zulia|Mara|Tamare' => null, // Crear automáticamente
    
    'Zulia|La Cañada de Urdaneta|Chiquinquira' => null, // Crear automáticamente
    'Zulia|La Cañada de Urdaneta|Concepcion' => null, // Crear automáticamente
    
    'Zulia|Lagunillas|Eleazar Lopez Contreras' => null, // Crear automáticamente
    
    'Zulia|Machiques de Perijá|Bartolome De Las Casas' => null, // Crear automáticamente
    'Zulia|Machiques de Perijá|Rio Negro' => null, // Crear automáticamente
    'Zulia|Machiques de Perijá|San Jose De Perija' => null, // Crear automáticamente
    
    'Zulia|Miranda|Altagracia' => null, // Crear automáticamente
    'Zulia|Miranda|Ana María Campos' => null, // Crear automáticamente
    'Zulia|Miranda|Faría' => null, // Crear automáticamente
    'Zulia|Miranda|San Antonio' => null, // Crear automáticamente
    'Zulia|Miranda|San José' => null, // Crear automáticamente
    
    'Zulia|San Francisco|Jose Domingo Rus' => null, // Crear automáticamente
    'Zulia|San Francisco|Marcial Hernandez' => null, // Crear automáticamente
    
    'Zulia|Santa Rita|Jose Cenovio Urribarri' => null, // Crear automáticamente
    'Zulia|Santa Rita|Pedro Lucas Urribarri' => null, // Crear automáticamente
    
    'Zulia|Sucre|Bobures' => null, // Crear automáticamente
    'Zulia|Sucre|El Batey' => null, // Crear automáticamente
    'Zulia|Sucre|Gibraltar' => null, // Crear automáticamente
    'Zulia|Sucre|Heras' => null, // Crear automáticamente
    'Zulia|Sucre|M.Arturo Celestino A' => null, // Crear automáticamente
    'Zulia|Sucre|Romulo Gallegos' => null, // Crear automáticamente
];

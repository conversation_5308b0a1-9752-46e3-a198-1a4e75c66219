<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Super Admin con todos los permisos
        $superAdmin = Role::query()->updateOrCreate(['name' => 'Super Admin']);
        $permissions = Permission::all()->pluck('name')->toArray();
        $superAdmin->givePermissionTo($permissions);

        // Coordinador de Personas - Gestión completa de personas
        $coordinador = Role::query()->updateOrCreate(['name' => 'Coordinador de Personas']);
        $coordinador->givePermissionTo([
            'access dashboard',
            'view people',
            'create people',
            'update people',
            'delete people',
            'export people',
            'import people',
            'assign people leaders',
            'create user from person',
            'view locations',
            'view voting_centers',
            'search voting_centers',
            'view voting_center_stats',
            'view electoral_events',
            'view mobilizations',
            'view reports',
            'view dashboard_stats',
        ]);

        // Líder 1x10 - Gestión limitada de su grupo
        $lider1x10 = Role::query()->updateOrCreate(['name' => 'Líder 1x10']);
        $lider1x10->givePermissionTo([
            'access dashboard',
            'view own people', // Solo las de su grupo
            'update people', // Solo las de su grupo
            'view locations',
            'view voting_centers',
            'search voting_centers',
            'view electoral_events',
            'view mobilizations',
            'view dashboard_stats',
        ]);

        // Militante - Acceso básico
        $militante = Role::query()->updateOrCreate(['name' => 'Militante']);
        $militante->givePermissionTo([
            'access dashboard',
            'view locations',
            'view voting_centers',
            'search voting_centers',
            'view electoral_events',
            'view mobilizations',
        ]);

        // Votante - Acceso muy limitado
        $votante = Role::query()->updateOrCreate(['name' => 'Votante']);
        $votante->givePermissionTo([
            'access dashboard',
            'view voting_centers',
            'search voting_centers',
        ]);

        // Operador de Datos - Para entrada de información
        $operador = Role::query()->updateOrCreate(['name' => 'Operador de Datos']);
        $operador->givePermissionTo([
            'access dashboard',
            'view people',
            'create people',
            'update people',
            'view locations',
            'view voting_centers',
            'search voting_centers',
            'export people',
        ]);

        // Coordinador Electoral - Gestión de centros de votación y eventos
        $coordinadorElectoral = Role::query()->updateOrCreate(['name' => 'Coordinador Electoral']);
        $coordinadorElectoral->givePermissionTo([
            'access dashboard',
            'view locations',
            'view voting_centers',
            'create voting_centers',
            'update voting_centers',
            'export voting_centers',
            'import voting_centers',
            'search voting_centers',
            'view voting_center_stats',
            'view voting_tables',
            'create voting_tables',
            'update voting_tables',
            'view electoral_events',
            'create electoral_events',
            'update electoral_events',
            'view mobilizations',
            'view reports',
            'view dashboard_stats',
        ]);

        // Analista de Datos - Solo lectura y reportes
        $analista = Role::query()->updateOrCreate(['name' => 'Analista de Datos']);
        $analista->givePermissionTo([
            'access dashboard',
            'view people',
            'view locations',
            'view voting_centers',
            'search voting_centers',
            'view voting_center_stats',
            'view electoral_events',
            'view mobilizations',
            'view reports',
            'create reports',
            'export reports',
            'export people',
            'export voting_centers',
            'view dashboard_stats',
        ]);
    }
}

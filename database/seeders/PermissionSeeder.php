<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON><PERSON>\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $permissions = [

            'access dashboard',

            'impersonate',

            // Permisos de administración de usuarios
            'view users',
            'create users',
            'update users',
            'delete users',

            // Permisos de roles
            'view roles',
            'create roles',
            'update roles',
            'delete roles',

            // Permisos de permisos
            'view permissions',
            'create permissions',
            'update permissions',
            'delete permissions',

            // Permisos para gestión de personas
            'view people',
            'create people',
            'update people',
            'delete people',
            'export people',
            'import people',
            'assign people leaders',
            'create user from person',
            'view own people', // Para líderes 1x10 - solo ver su grupo

            // Permisos para ubicaciones geográficas
            'view locations',
            'create locations',
            'update locations',
            'delete locations',
            'export locations',
            'import locations',

            // Permisos para centros de votación
            'view voting_centers',
            'create voting_centers',
            'update voting_centers',
            'delete voting_centers',
            'export voting_centers',
            'import voting_centers',
            'search voting_centers',
            'view voting_center_stats',

            // Permisos para mesas electorales
            'view voting_tables',
            'create voting_tables',
            'update voting_tables',
            'delete voting_tables',

            // Permisos para eventos electorales
            'view electoral_events',
            'create electoral_events',
            'update electoral_events',
            'delete electoral_events',
            'export electoral_events',

            // Permisos para movilizaciones
            'view mobilizations',
            'create mobilizations',
            'update mobilizations',
            'delete mobilizations',
            'export mobilizations',

            // Permisos para reportes y estadísticas
            'view reports',
            'create reports',
            'export reports',
            'view dashboard_stats',

            // Permisos para configuración del sistema
            'view system_config',
            'update system_config',
            'view system_logs',
            'clear system_cache',

            // Permisos para auditoría
            'view audit_logs',
            'export audit_logs',
        ];

        foreach ($permissions as $permission) {
            Permission::query()->updateOrCreate([
                'name' => $permission,
            ]);
        }

    }
}

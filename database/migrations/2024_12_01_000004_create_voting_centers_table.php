<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voting_centers', function (Blueprint $table) {
            $table->id();
            $table->string('code', 20)->unique()->comment('Código único del centro de votación');
            $table->string('old_code', 20)->nullable()->comment('Código anterior del centro');
            $table->string('name')->comment('Nombre del centro de votación');
            $table->text('address')->nullable()->comment('Dirección del centro');

            // Foreign keys para ubicación
            $table->foreignId('state_id')->constrained('states')->onDelete('cascade');
            $table->foreignId('municipality_id')->constrained('municipalities')->onDelete('cascade');
            $table->foreignId('parish_id')->constrained('parishes')->onDelete('cascade');

            // Información electoral
            $table->integer('total_voters')->default(0)->comment('Total de electores registrados');
            $table->integer('total_tables')->default(0)->comment('Número total de mesas electorales');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');

            // Coordenadas GPS
            $table->decimal('latitude', 10, 8)->nullable()->comment('Latitud GPS del centro');
            $table->decimal('longitude', 11, 8)->nullable()->comment('Longitud GPS del centro');

            // Información adicional
            $table->json('additional_info')->nullable()->comment('Información adicional en formato JSON');

            // Compatibilidad con versión anterior
            $table->boolean('active')->default(true)->comment('Estado activo (compatibilidad)');

            $table->timestamps();

            // Índices para optimizar consultas
            $table->index(['state_id', 'municipality_id', 'parish_id'], 'voting_centers_location_index');
            $table->index('status', 'voting_centers_status_index');
            $table->index('code', 'voting_centers_code_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voting_centers');
    }
};

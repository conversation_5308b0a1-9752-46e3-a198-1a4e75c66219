<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('voter_captures', function (Blueprint $table) {
            // Add audit fields
            $table->foreignId('created_by')->nullable()->after('additional_data')->constrained('users')->onDelete('set null');
            $table->foreignId('updated_by')->nullable()->after('created_by')->constrained('users')->onDelete('set null');
            
            // Add soft deletes
            $table->softDeletes()->after('updated_by');
            
            // Add priority field for better organization
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium')->after('status');
            
            // Add source details for better tracking
            $table->string('source_details')->nullable()->after('capture_source');
            
            // Add geolocation fields
            $table->decimal('latitude', 10, 8)->nullable()->after('address');
            $table->decimal('longitude', 11, 8)->nullable()->after('latitude');
            
            // Add campaign tracking
            $table->string('campaign_id')->nullable()->after('additional_data');
            $table->string('referral_code')->nullable()->after('campaign_id');
            
            // Add quality score
            $table->integer('quality_score')->nullable()->after('commitment_level');
            
            // Add indexes for performance
            $table->index(['priority']);
            $table->index(['campaign_id']);
            $table->index(['quality_score']);
            $table->index(['created_by']);
            $table->index(['updated_by']);
            $table->index(['deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('voter_captures', function (Blueprint $table) {
            $table->dropForeign(['created_by']);
            $table->dropForeign(['updated_by']);
            $table->dropColumn([
                'created_by',
                'updated_by',
                'deleted_at',
                'priority',
                'source_details',
                'latitude',
                'longitude',
                'campaign_id',
                'referral_code',
                'quality_score'
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('voter_captures', function (Blueprint $table) {
            $table->id();

            // Voter information
            $table->string('first_name');
            $table->string('last_name');
            $table->string('document_number', 20)->unique();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['M', 'F', 'O'])->nullable();

            // Contact information
            $table->string('phone', 20)->nullable();
            $table->string('secondary_phone', 20)->nullable();
            $table->string('email')->nullable();
            $table->text('address')->nullable();

            // Geographic location
            $table->foreignId('state_id')->nullable()->constrained('states')->onDelete('set null');
            $table->foreignId('municipality_id')->nullable()->constrained('municipalities')->onDelete('set null');
            $table->foreignId('parish_id')->nullable()->constrained('parishes')->onDelete('set null');

            // Electoral information
            $table->foreignId('voting_center_id')->nullable()->constrained('voting_centers')->onDelete('set null');
            $table->string('voting_table', 10)->nullable();

            // Capture management
            $table->foreignId('responsible_id')->constrained('people')->onDelete('cascade');
            $table->enum('capture_status', ['pending', 'contacted', 'confirmed', 'not_interested', 'unreachable'])->default('pending');
            $table->date('capture_date');
            $table->date('last_contact_date')->nullable();
            $table->date('next_contact_date')->nullable();
            $table->text('contact_notes')->nullable();

            // Capture source and method
            $table->enum('capture_source', ['door_to_door', 'phone_call', 'social_media', 'event', 'referral', 'other'])->default('door_to_door');
            $table->enum('contact_method', ['phone', 'whatsapp', 'visit', 'email', 'social_media'])->default('phone');
            $table->integer('contact_attempts')->default(0);

            // Interest and commitment level
            $table->enum('interest_level', ['high', 'medium', 'low', 'none'])->nullable();
            $table->enum('commitment_level', ['committed', 'likely', 'uncertain', 'unlikely'])->nullable();
            $table->boolean('willing_to_vote')->default(false);
            $table->boolean('willing_to_mobilize')->default(false);

            // Follow-up and reminders
            $table->boolean('needs_follow_up')->default(true);
            $table->date('follow_up_date')->nullable();
            $table->boolean('reminder_sent')->default(false);
            $table->timestamp('last_reminder_sent')->nullable();

            // Conversion to person
            $table->foreignId('person_id')->nullable()->constrained('people')->onDelete('set null');
            $table->boolean('converted_to_person')->default(false);
            $table->timestamp('conversion_date')->nullable();

            // Status and metadata
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->text('notes')->nullable();
            $table->json('additional_data')->nullable();

            $table->timestamps();

            // Indexes to optimize searches
            $table->index(['first_name', 'last_name']);
            $table->index(['document_number']);
            $table->index(['responsible_id']);
            $table->index(['capture_status']);
            $table->index(['capture_date']);
            $table->index(['next_contact_date']);
            $table->index(['follow_up_date']);
            $table->index(['status']);
            $table->index(['state_id', 'municipality_id', 'parish_id']);
            $table->index(['converted_to_person']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('voter_captures');
    }
};

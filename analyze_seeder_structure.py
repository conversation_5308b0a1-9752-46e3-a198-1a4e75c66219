import pandas as pd
import re

def extract_seeder_data():
    """Extract all data from UbicacionesSeeder.php"""
    
    # Read the seeder file
    with open('database/seeders/UbicacionesSeeder.php', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract states
    states_pattern = r"'name' => '([^']+)', 'code' => '([^']+)'"
    states_matches = re.findall(states_pattern, content)
    
    print("🗺️ ESTADOS EN EL SEEDER:")
    seeder_states = {}
    for name, code in states_matches[:25]:  # First 25 are states
        seeder_states[code] = name
        print(f"   {code}: {name}")
    
    # Extract municipalities by finding the getMunicipalitiesByState method
    municipalities_section = re.search(r'private function getMunicipalitiesByState.*?return \$municipalities\[\$stateCode\] \?\? \[\];', content, re.DOTALL)
    
    if municipalities_section:
        municipalities_content = municipalities_section.group(0)
        
        # Extract municipalities by state
        state_sections = re.findall(r"// ([^\n]+)\n\s+'([^']+)' => \[(.*?)\],", municipalities_content, re.DOTALL)
        
        seeder_municipalities = {}
        print(f"\n🏛️ MUNICIPIOS EN EL SEEDER:")
        
        for comment, state_code, municipalities_data in state_sections:
            if state_code in seeder_states:
                state_name = seeder_states[state_code]
                municipalities = re.findall(r"\['name' => '([^']+)', 'code' => '([^']+)'\]", municipalities_data)
                seeder_municipalities[state_name] = [name for name, code in municipalities]
                print(f"\n   {state_name} ({state_code}):")
                for name, code in municipalities:
                    print(f"      {code}: {name}")
    
    # Extract parishes
    parishes_section = re.search(r'private function getParishesByMunicipality.*?return \$parishes\[\$municipalityCode\] \?\? \[\];', content, re.DOTALL)
    
    if parishes_section:
        parishes_content = parishes_section.group(0)
        
        # Extract parishes by municipality code
        municipality_sections = re.findall(r"'([^']+)' => \[(.*?)\],", parishes_content, re.DOTALL)
        
        seeder_parishes = {}
        print(f"\n🏘️ PARROQUIAS EN EL SEEDER (primeras 10 municipios):")
        
        count = 0
        for municipality_code, parishes_data in municipality_sections:
            if count >= 10:  # Limit output
                break
                
            parishes = re.findall(r"\['name' => '([^']+)', 'code' => '([^']+)'\]", parishes_data)
            seeder_parishes[municipality_code] = [name for name, code in parishes]
            
            print(f"\n   {municipality_code}:")
            for name, code in parishes:
                print(f"      {code}: {name}")
            count += 1
    
    return seeder_states, seeder_municipalities, seeder_parishes

def analyze_excel_problems():
    """Analyze specific problems from Excel data"""
    
    # Read Excel data
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    df_clean = df.iloc[:, :13].copy()
    df_clean = df_clean[df_clean['estado_corto'] != 'EXTERIOR']
    
    print(f"\n🔍 ANÁLISIS DE PROBLEMAS ESPECÍFICOS:")
    
    # Check Atabapo parishes issue
    atabapo_data = df_clean[df_clean['municipio_corto'] == 'ATABAPO']
    print(f"\n📍 Municipio ATABAPO en Excel:")
    print(f"   Total centros: {len(atabapo_data)}")
    print(f"   Parroquias únicas:")
    for parish in atabapo_data['parroquia_corto'].unique():
        count = len(atabapo_data[atabapo_data['parroquia_corto'] == parish])
        print(f"      {parish}: {count} centros")
    
    # Check Anaco parishes issue  
    anaco_data = df_clean[df_clean['municipio_corto'] == 'ANACO']
    print(f"\n📍 Municipio ANACO en Excel:")
    print(f"   Total centros: {len(anaco_data)}")
    print(f"   Parroquias únicas:")
    for parish in anaco_data['parroquia_corto'].unique():
        count = len(anaco_data[anaco_data['parroquia_corto'] == parish])
        print(f"      {parish}: {count} centros")
    
    # Check Zulia municipalities issues
    zulia_data = df_clean[df_clean['estado_corto'] == 'ZULIA']
    print(f"\n📍 Estado ZULIA en Excel:")
    print(f"   Total centros: {len(zulia_data)}")
    print(f"   Municipios únicos (primeros 10):")
    municipalities = zulia_data['municipio_corto'].value_counts()
    for municipality, count in municipalities.head(10).items():
        print(f"      {municipality}: {count} centros")
    
    # Check problematic municipalities
    problematic = ['ROSARIO DE PERIJA', 'VALMORE RODRIGUEZ', 'MIRANDA']
    for municipality in problematic:
        mun_data = zulia_data[zulia_data['municipio_corto'] == municipality]
        if len(mun_data) > 0:
            print(f"\n   📍 {municipality}:")
            print(f"      Total centros: {len(mun_data)}")
            print(f"      Parroquias:")
            for parish in mun_data['parroquia_corto'].unique():
                count = len(mun_data[mun_data['parroquia_corto'] == parish])
                print(f"         {parish}: {count} centros")

if __name__ == "__main__":
    seeder_states, seeder_municipalities, seeder_parishes = extract_seeder_data()
    analyze_excel_problems()

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Voter Captures Module Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the voter captures module
    | that implements the 1x10 strategy for voter outreach and management.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Module Settings
    |--------------------------------------------------------------------------
    |
    | General settings for the voter captures module.
    |
    */
    'enabled' => env('VOTER_CAPTURES_ENABLED', true),
    'default_per_page' => env('VOTER_CAPTURES_PER_PAGE', 10),
    'max_per_page' => env('VOTER_CAPTURES_MAX_PER_PAGE', 100),

    /*
    |--------------------------------------------------------------------------
    | Capture Limits
    |--------------------------------------------------------------------------
    |
    | Limits for captures per responsible person and other constraints.
    |
    */
    'limits' => [
        'max_captures_per_responsible' => env('MAX_CAPTURES_PER_RESPONSIBLE', 50),
        'max_daily_captures' => env('MAX_DAILY_CAPTURES', 20),
        'max_contact_attempts' => env('MAX_CONTACT_ATTEMPTS', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Contact and Follow-up Settings
    |--------------------------------------------------------------------------
    |
    | Settings for contact attempts and follow-up scheduling.
    |
    */
    'contact' => [
        'default_next_contact_days' => env('DEFAULT_NEXT_CONTACT_DAYS', 3),
        'max_days_without_contact' => env('MAX_DAYS_WITHOUT_CONTACT', 7),
        'follow_up_reminder_days' => env('FOLLOW_UP_REMINDER_DAYS', 1),
        'auto_archive_after_days' => env('AUTO_ARCHIVE_AFTER_DAYS', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Reminder Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic reminder notifications.
    |
    */
    'reminders' => [
        'enabled' => env('CAPTURE_REMINDERS_ENABLED', true),
        'send_time' => env('CAPTURE_REMINDERS_TIME', '09:00'),
        'frequency_hours' => env('CAPTURE_REMINDERS_FREQUENCY', 24),
        'max_reminders_per_capture' => env('MAX_REMINDERS_PER_CAPTURE', 3),
        'channels' => ['mail', 'database'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Settings for various notifications in the captures module.
    |
    */
    'notifications' => [
        'enabled' => env('CAPTURE_NOTIFICATIONS_ENABLED', true),
        'channels' => ['mail', 'database'],
        'events' => [
            'capture_created' => env('NOTIFY_CAPTURE_CREATED', true),
            'capture_confirmed' => env('NOTIFY_CAPTURE_CONFIRMED', true),
            'capture_converted' => env('NOTIFY_CAPTURE_CONVERTED', true),
            'capture_overdue' => env('NOTIFY_CAPTURE_OVERDUE', true),
        ],
        'recipients' => [
            'responsible' => true,
            'coordinators' => env('NOTIFY_COORDINATORS', true),
            'admins' => env('NOTIFY_ADMINS', false),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Custom validation rules for capture data.
    |
    */
    'validation' => [
        'document_number' => [
            'required',
            'string',
            'max:20',
            'unique:voter_captures,document_number',
            'regex:/^[0-9]+$/', // Only numbers
        ],
        'phone' => [
            'nullable',
            'string',
            'max:20',
            'regex:/^[\+]?[0-9\-\(\)\s]+$/', // Phone format
        ],
        'email' => [
            'nullable',
            'email',
            'max:255',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Status Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for capture statuses and their behavior.
    |
    */
    'statuses' => [
        'pending' => [
            'color' => 'yellow',
            'icon' => 'clock',
            'requires_follow_up' => true,
            'can_convert' => false,
        ],
        'contacted' => [
            'color' => 'blue',
            'icon' => 'phone',
            'requires_follow_up' => true,
            'can_convert' => false,
        ],
        'confirmed' => [
            'color' => 'green',
            'icon' => 'check-circle',
            'requires_follow_up' => false,
            'can_convert' => true,
        ],
        'not_interested' => [
            'color' => 'red',
            'icon' => 'x-circle',
            'requires_follow_up' => false,
            'can_convert' => false,
        ],
        'unreachable' => [
            'color' => 'gray',
            'icon' => 'exclamation-triangle',
            'requires_follow_up' => false,
            'can_convert' => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Export Settings
    |--------------------------------------------------------------------------
    |
    | Settings for data export functionality.
    |
    */
    'export' => [
        'enabled' => env('CAPTURE_EXPORT_ENABLED', true),
        'formats' => ['csv', 'xlsx', 'pdf'],
        'max_records' => env('CAPTURE_EXPORT_MAX_RECORDS', 10000),
        'chunk_size' => env('CAPTURE_EXPORT_CHUNK_SIZE', 1000),
    ],

    /*
    |--------------------------------------------------------------------------
    | Import Settings
    |--------------------------------------------------------------------------
    |
    | Settings for data import functionality.
    |
    */
    'import' => [
        'enabled' => env('CAPTURE_IMPORT_ENABLED', true),
        'formats' => ['csv', 'xlsx'],
        'max_file_size' => env('CAPTURE_IMPORT_MAX_SIZE', '10MB'),
        'max_records' => env('CAPTURE_IMPORT_MAX_RECORDS', 5000),
        'chunk_size' => env('CAPTURE_IMPORT_CHUNK_SIZE', 100),
        'required_columns' => [
            'first_name',
            'last_name',
            'document_number',
            'responsible_id',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Statistics and Reporting
    |--------------------------------------------------------------------------
    |
    | Settings for statistics and reporting features.
    |
    */
    'statistics' => [
        'enabled' => env('CAPTURE_STATISTICS_ENABLED', true),
        'cache_duration' => env('CAPTURE_STATS_CACHE_DURATION', 3600), // 1 hour
        'dashboard_widgets' => [
            'total_captures',
            'pending_captures',
            'confirmed_captures',
            'conversion_rate',
            'overdue_captures',
            'captures_by_responsible',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings to optimize performance for large datasets.
    |
    */
    'performance' => [
        'enable_caching' => env('CAPTURE_CACHING_ENABLED', true),
        'cache_prefix' => 'voter_captures',
        'cache_duration' => env('CAPTURE_CACHE_DURATION', 1800), // 30 minutes
        'eager_load_relations' => [
            'state',
            'municipality',
            'parish',
            'votingCenter',
            'responsible',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | Security-related settings for the captures module.
    |
    */
    'security' => [
        'encrypt_sensitive_data' => env('CAPTURE_ENCRYPT_DATA', false),
        'audit_changes' => env('CAPTURE_AUDIT_CHANGES', true),
        'require_2fa_for_exports' => env('CAPTURE_REQUIRE_2FA_EXPORTS', false),
        'log_access' => env('CAPTURE_LOG_ACCESS', true),
    ],

];

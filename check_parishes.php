<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

echo "🔍 VERIFICANDO PARROQUIAS ESPECÍFICAS\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Casos problemáticos del seeder
$testCases = [
    ['state' => 'Anzoátegui', 'municipality' => 'Anaco', 'parish' => 'Anaco'],
    ['state' => 'Distrito Capital', 'municipality' => 'Libertador', 'parish' => 'Sucre'],
    ['state' => 'Miranda', 'municipality' => 'Sucre', 'parish' => 'Petare'],
    ['state' => 'Carabobo', 'municipality' => 'Valencia', 'parish' => 'Miguel Peña'],
];

foreach ($testCases as $i => $case) {
    echo "📍 Caso " . ($i + 1) . ": {$case['state']} > {$case['municipality']} > {$case['parish']}\n";
    
    // Buscar estado
    $state = State::where('name', $case['state'])->first();
    if (!$state) {
        echo "   ❌ Estado '{$case['state']}' no encontrado\n";
        echo "   📋 Estados disponibles: " . State::pluck('name')->implode(', ') . "\n\n";
        continue;
    }
    echo "   ✅ Estado encontrado: {$state->name} (ID: {$state->id})\n";
    
    // Buscar municipio
    $municipality = Municipality::where('name', $case['municipality'])
                                ->where('state_id', $state->id)
                                ->first();
    if (!$municipality) {
        echo "   ❌ Municipio '{$case['municipality']}' no encontrado\n";
        $availableMunicipalities = Municipality::where('state_id', $state->id)->pluck('name')->toArray();
        echo "   📋 Municipios disponibles en {$state->name}: " . implode(', ', array_slice($availableMunicipalities, 0, 5)) . "...\n\n";
        continue;
    }
    echo "   ✅ Municipio encontrado: {$municipality->name} (ID: {$municipality->id})\n";
    
    // Buscar parroquia
    $parish = Parish::where('name', $case['parish'])
                   ->where('municipality_id', $municipality->id)
                   ->first();
    if (!$parish) {
        echo "   ❌ Parroquia '{$case['parish']}' no encontrada\n";
        $availableParishes = Parish::where('municipality_id', $municipality->id)->pluck('name')->toArray();
        echo "   📋 Parroquias disponibles en {$municipality->name}: " . implode(', ', $availableParishes) . "\n";
    } else {
        echo "   ✅ Parroquia encontrada: {$parish->name} (ID: {$parish->id})\n";
    }
    
    echo "\n";
}

// Verificar algunos casos específicos de Anzoátegui
echo "🔍 VERIFICANDO ANZOÁTEGUI ESPECÍFICAMENTE\n";
echo "-" . str_repeat("-", 40) . "\n";

$anzoategui = State::where('name', 'Anzoátegui')->first();
if ($anzoategui) {
    $anacoMunicipality = Municipality::where('name', 'Anaco')->where('state_id', $anzoategui->id)->first();
    if ($anacoMunicipality) {
        echo "📍 Municipio Anaco encontrado\n";
        $parishes = Parish::where('municipality_id', $anacoMunicipality->id)->pluck('name')->toArray();
        echo "📋 Parroquias en Anaco: " . implode(', ', $parishes) . "\n\n";
    } else {
        echo "❌ Municipio Anaco no encontrado en Anzoátegui\n";
        $municipalities = Municipality::where('state_id', $anzoategui->id)->pluck('name')->toArray();
        echo "📋 Municipios en Anzoátegui: " . implode(', ', array_slice($municipalities, 0, 10)) . "...\n\n";
    }
}

echo "✅ Verificación completada\n";

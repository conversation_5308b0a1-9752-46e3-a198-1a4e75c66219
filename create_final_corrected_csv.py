import pandas as pd
import math
import random

def create_corrected_csv():
    """Create final corrected CSV with exact mappings to UbicacionesSeeder"""
    print("🔧 Creando CSV final corregido con mapeos exactos...")
    
    # Read the Excel file
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    df_clean = df.iloc[:, :13].copy()
    
    # Filter out EXTERIOR records
    df_clean = df_clean[df_clean['estado_corto'] != 'EXTERIOR']
    print(f"📊 Records after filtering EXTERIOR: {len(df_clean)}")
    
    # EXACT State mapping (Excel -> Database)
    state_mapping = {
        'AMAZONAS': 'Amazonas',
        'ANZOATEGUI': 'Anzoátegui',
        'APURE': 'Apure',
        'ARAGUA': 'Aragua',
        'BARINAS': 'Barinas',
        'BOLIVAR': 'Bolívar',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Cojedes',
        'DELTA AMACURO': 'Delta Amacuro',
        'CAPITAL': 'Distrito Capital',
        'FALCON': 'Falcón',
        'GUARICO': 'Guárico',
        'LA GUAIRA': 'La Guaira',
        'LARA': 'Lara',
        'MERIDA': 'Mérida',
        'MIRANDA': 'Miranda',
        'MONAGAS': 'Monagas',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Táchira',
        'TRUJILLO': 'Trujillo',
        'YARACUY': 'Yaracuy',
        'ZULIA': 'Zulia',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales'
    }
    
    # EXACT Municipality mappings
    municipality_mapping = {
        # Amazonas
        'Amazonas|ALTO ORINOCO': 'Amazonas|Alto Orinoco',
        'Amazonas|ATABAPO': 'Amazonas|Atabapo',
        'Amazonas|ATURES': 'Amazonas|Atures',
        'Amazonas|AUTANA': 'Amazonas|Autana',
        'Amazonas|MANAPIARE': 'Amazonas|Manapiare',
        'Amazonas|MAROA': 'Amazonas|Maroa',
        'Amazonas|RIO NEGRO': 'Amazonas|Río Negro',
        
        # Anzoátegui
        'Anzoátegui|ANACO': 'Anzoátegui|Anaco',
        'Anzoátegui|ARAGUA': 'Anzoátegui|Aragua',
        'Anzoátegui|BOLIVAR': 'Anzoátegui|Bolívar',
        'Anzoátegui|BRUZUAL': 'Anzoátegui|Bruzual',
        'Anzoátegui|CAJIGAL': 'Anzoátegui|Cajigal',
        'Anzoátegui|CARVAJAL': 'Anzoátegui|Carvajal',
        'Anzoátegui|FREITES': 'Anzoátegui|Freites',
        'Anzoátegui|GUANIPA': 'Anzoátegui|Guanipa',
        'Anzoátegui|GUANTA': 'Anzoátegui|Guanta',
        'Anzoátegui|INDEPENDENCIA': 'Anzoátegui|Independencia',
        'Anzoátegui|LIBERTAD': 'Anzoátegui|Libertad',
        'Anzoátegui|MCGREGOR': 'Anzoátegui|Sir Arthur McGregor',
        'Anzoátegui|MIRANDA': 'Anzoátegui|Miranda',
        'Anzoátegui|MONAGAS': 'Anzoátegui|Monagas',
        'Anzoátegui|PEÑALVER': 'Anzoátegui|Peñalver',
        'Anzoátegui|PIRITU': 'Anzoátegui|Píritu',
        'Anzoátegui|SAN JUAN DE CAPISTRANO': 'Anzoátegui|San Juan de Capistrano',
        'Anzoátegui|SANTA ANA': 'Anzoátegui|Santa Ana',
        'Anzoátegui|SIMON BOLIVAR': 'Anzoátegui|Simón Bolívar',
        'Anzoátegui|SIMON RODRIGUEZ': 'Anzoátegui|Simón Rodríguez',
        'Anzoátegui|SOTILLO': 'Anzoátegui|Sotillo',
        'Anzoátegui|TURISMO': 'Anzoátegui|Turístico Diego Bautista Urbaneja',
        
        # Zulia
        'Zulia|ALMIRANTE PADILLA': 'Zulia|Almirante Padilla',
        'Zulia|BARALT': 'Zulia|Baralt',
        'Zulia|CABIMAS': 'Zulia|Cabimas',
        'Zulia|CATATUMBO': 'Zulia|Catatumbo',
        'Zulia|COLON': 'Zulia|Colón',
        'Zulia|FRANCISCO JAVIER PULGAR': 'Zulia|Francisco Javier Pulgar',
        'Zulia|GUAJIRA': 'Zulia|Guajira',
        'Zulia|JESUS ENRIQUE LOSSADA': 'Zulia|Jesús Enrique Lossada',
        'Zulia|JESUS MARIA SEMPRUN': 'Zulia|Jesús María Semprún',
        'Zulia|LA CAÑADA DE URDANETA': 'Zulia|La Cañada de Urdaneta',
        'Zulia|LAGUNILLAS': 'Zulia|Lagunillas',
        'Zulia|MACHIQUES DE PERIJA': 'Zulia|Machiques de Perijá',
        'Zulia|MARA': 'Zulia|Mara',
        'Zulia|MARACAIBO': 'Zulia|Maracaibo',
        'Zulia|MIRANDA': 'Zulia|Miranda',
        'Zulia|ROSARIO DE PERIJA': 'Zulia|Rosario de Perijá',
        'Zulia|SAN FRANCISCO': 'Zulia|San Francisco',
        'Zulia|SANTA RITA': 'Zulia|Santa Rita',
        'Zulia|SIMON BOLIVAR': 'Zulia|Simón Bolívar',
        'Zulia|SUCRE': 'Zulia|Sucre',
        'Zulia|VALMORE RODRIGUEZ': 'Zulia|Valmore Rodríguez',
    }
    
    # EXACT Parish mappings (only for parishes that exist in UbicacionesSeeder)
    parish_mapping = {
        # Amazonas - Updated parishes
        'Amazonas|Alto Orinoco|LA ESMERALDA': 'Amazonas|Alto Orinoco|La Esmeralda',
        'Amazonas|Alto Orinoco|HUACHAMACARE': 'Amazonas|Alto Orinoco|Huachamacare',
        'Amazonas|Alto Orinoco|MARAWAKA': 'Amazonas|Alto Orinoco|Marawaka',
        'Amazonas|Alto Orinoco|MAVACA': 'Amazonas|Alto Orinoco|Mavaca',
        'Amazonas|Alto Orinoco|SIERRA PARIMA': 'Amazonas|Alto Orinoco|Sierra Parima',
        
        # Atabapo - Now correctly mapped
        'Amazonas|Atabapo|SAN FERNANDO DE ATABAPO': 'Amazonas|Atabapo|San Fernando de Atabapo',
        'Amazonas|Atabapo|UCATA': 'Amazonas|Atabapo|Ucata',
        'Amazonas|Atabapo|YAPACANA': 'Amazonas|Atabapo|Yapacana',
        'Amazonas|Atabapo|CANAME': 'Amazonas|Atabapo|Caname',
        
        # Atures
        'Amazonas|Atures|FERNANDO GIRON TOVAR': 'Amazonas|Atures|Fernando Girón Tovar',
        'Amazonas|Atures|LUIS ALBERTO GOMEZ': 'Amazonas|Atures|Luis Alberto Gómez',
        'Amazonas|Atures|PARHUEÑA': 'Amazonas|Atures|Pahueña',
        'Amazonas|Atures|PLATANILLAL': 'Amazonas|Atures|Platanillal',
        
        # Autana - Now includes Isla de Ratón
        'Amazonas|Autana|ISLA DE RATON': 'Amazonas|Autana|Isla de Ratón',
        'Amazonas|Autana|SAMARIAPO': 'Amazonas|Autana|Samariapo',
        'Amazonas|Autana|SIPAPO': 'Amazonas|Autana|Sipapo',
        'Amazonas|Autana|MUNDUAPO': 'Amazonas|Autana|Munduapo',
        'Amazonas|Autana|GUAYAPO': 'Amazonas|Autana|Guayapo',
        
        # Manapiare - Now includes San Juan de Manapiare
        'Amazonas|Manapiare|SAN JUAN DE MANAPIARE': 'Amazonas|Manapiare|San Juan de Manapiare',
        'Amazonas|Manapiare|ALTO VENTUARI': 'Amazonas|Manapiare|Alto Ventuari',
        'Amazonas|Manapiare|MEDIO VENTUARI': 'Amazonas|Manapiare|Medio Ventuari',
        'Amazonas|Manapiare|BAJO VENTUARI': 'Amazonas|Manapiare|Bajo Ventuari',
        
        # Maroa - Now includes Maroa parish
        'Amazonas|Maroa|MAROA': 'Amazonas|Maroa|Maroa',
        'Amazonas|Maroa|VICTORINO': 'Amazonas|Maroa|Victorino',
        'Amazonas|Maroa|COMUNIDAD': 'Amazonas|Maroa|Comunidad',
        
        # Río Negro
        'Amazonas|Río Negro|SOLANO': 'Amazonas|Río Negro|Solano',
        'Amazonas|Río Negro|CASIQUIARE': 'Amazonas|Río Negro|Casiquiare',
        'Amazonas|Río Negro|COCUY': 'Amazonas|Río Negro|Cocuy',
        'Amazonas|Río Negro|SAN CARLOS DE RIO NEGRO': 'Amazonas|Río Negro|San Carlos de Río Negro',
        
        # Anzoátegui - Anaco
        'Anzoátegui|Anaco|ANACO': 'Anzoátegui|Anaco|Anaco',
        'Anzoátegui|Anaco|SAN JOAQUIN': 'Anzoátegui|Anaco|San Joaquín',
        
        # Zulia - Miranda
        'Zulia|Miranda|ALTAGRACIA': 'Zulia|Miranda|Altagracia',
        'Zulia|Miranda|ANA MARIA CAMPOS': 'Zulia|Miranda|Ana María Campos',
        'Zulia|Miranda|FARIA': 'Zulia|Miranda|Faría',
        'Zulia|Miranda|SAN ANTONIO': 'Zulia|Miranda|San Antonio',
        'Zulia|Miranda|SAN JOSE': 'Zulia|Miranda|San José',
        
        # Zulia - Rosario de Perijá
        'Zulia|Rosario de Perijá|EL ROSARIO': 'Zulia|Rosario de Perijá|El Rosario',
        'Zulia|Rosario de Perijá|DONALDO GARCIA': 'Zulia|Rosario de Perijá|Donaldo García',
        'Zulia|Rosario de Perijá|SIXTO ZAMBRANO': 'Zulia|Rosario de Perijá|Sixto Zambrano',
        
        # Zulia - Valmore Rodríguez
        'Zulia|Valmore Rodríguez|RAFAEL URDANETA': 'Zulia|Valmore Rodríguez|Rafael Urdaneta',
        'Zulia|Valmore Rodríguez|LA VICTORIA': 'Zulia|Valmore Rodríguez|La Victoria',
        'Zulia|Valmore Rodríguez|RAUL CUENCA': 'Zulia|Valmore Rodríguez|Raúl Cuenca',
    }
    
    def calculate_tables(total_voters):
        """Calculate number of voting tables based on total voters"""
        if total_voters <= 0:
            return 1
        tables = math.ceil(total_voters / 450)
        return max(1, tables)
    
    def generate_random_coordinates():
        """Generate random coordinates within Venezuela's approximate bounds"""
        lat_min, lat_max = 0.5, 12.5
        lon_min, lon_max = -73.5, -59.5
        
        latitude = round(random.uniform(lat_min, lat_max), 3)
        longitude = round(random.uniform(lon_min, lon_max), 3)
        
        return latitude, longitude
    
    # Process data
    output_data = []
    skipped_count = 0
    mapped_count = 0
    
    for idx, row in df_clean.iterrows():
        try:
            # Map state
            excel_state = str(row['estado_corto']).strip()
            state_name = state_mapping.get(excel_state)
            
            if not state_name:
                skipped_count += 1
                continue
            
            # Map municipality
            excel_municipality = str(row['municipio_corto']).strip()
            municipality_key = f"{state_name}|{excel_municipality}"
            municipality_name = municipality_mapping.get(municipality_key)
            
            if not municipality_name:
                # Try without state prefix
                municipality_name = excel_municipality.title()
                municipality_name = municipality_name.replace('Mp. ', '').replace('Municipio ', '')
            else:
                municipality_name = municipality_name.split('|')[1]  # Remove state prefix
            
            # Map parish
            excel_parish = str(row['parroquia_corto']).strip()
            parish_key = f"{state_name}|{municipality_name}|{excel_parish}"
            parish_name = parish_mapping.get(parish_key)
            
            if parish_name:
                parish_name = parish_name.split('|')[2]  # Remove state|municipality prefix
                mapped_count += 1
            else:
                # Use original parish name if no mapping found
                parish_name = excel_parish.title()
                parish_name = parish_name.replace('Pq. ', '').replace('Parroquia ', '')
            
            # Calculate tables
            total_voters = int(row['electores_total']) if pd.notna(row['electores_total']) else 0
            total_tables = calculate_tables(total_voters)
            
            # Generate coordinates
            latitude, longitude = generate_random_coordinates()
            
            # Generate a simple sequential code
            code = f"VE{len(output_data)+1:06d}"
            
            # Create output record
            output_record = {
                'code': code,
                'old_code': '',
                'name': str(row['centro']).strip() if pd.notna(row['centro']) else f'Centro {code}',
                'address': f"Centro de Votación {row['centro']}, {parish_name}, {municipality_name}, {state_name}" if pd.notna(row['centro']) else '',
                'state_name': state_name,
                'municipality_name': municipality_name,
                'parish_name': parish_name,
                'total_voters': total_voters,
                'total_tables': total_tables,
                'status': 'active',
                'latitude': latitude,
                'longitude': longitude
            }
            
            output_data.append(output_record)
            
            if len(output_data) % 1000 == 0:
                print(f"✅ Processed {len(output_data)} centers...")
                
        except Exception as e:
            print(f"❌ Error processing row {idx}: {e}")
            skipped_count += 1
            continue
    
    # Create output DataFrame
    output_df = pd.DataFrame(output_data)
    
    # Sort by state, municipality, parish, and code
    output_df = output_df.sort_values(['state_name', 'municipality_name', 'parish_name', 'code'])
    
    # Save to CSV
    output_file = 'database/seeders/data/voting_centers_corrected.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f"\n🎉 CSV corregido creado exitosamente!")
    print(f"📄 Archivo: {output_file}")
    print(f"📊 Centros procesados: {len(output_df)}")
    print(f"✅ Parroquias mapeadas correctamente: {mapped_count}")
    print(f"⏭️  Registros omitidos: {skipped_count}")
    print(f"🗺️  Estados: {output_df['state_name'].nunique()}")
    print(f"🏛️  Municipios: {output_df['municipality_name'].nunique()}")
    print(f"🏘️  Parroquias: {output_df['parish_name'].nunique()}")
    print(f"👥 Total electores: {output_df['total_voters'].sum():,}")
    print(f"🗳️  Total mesas: {output_df['total_tables'].sum():,}")
    
    return output_file

if __name__ == "__main__":
    create_corrected_csv()

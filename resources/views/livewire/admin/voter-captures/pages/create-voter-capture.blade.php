<section class="w-full">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.voter-captures.index') }}">{{ __('voter_captures.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ __('voter_captures.add_capture') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <x-page-heading>
        <x-slot:title>{{ __('voter_captures.add_capture') }}</x-slot:title>
        <x-slot:subtitle>{{ __('voter_captures.title_description') }}</x-slot:subtitle>
    </x-page-heading>

    <form wire:submit="createCapture" class="space-y-6">
        <!-- Personal Data Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.personal_data') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.first_name') }} *</flux:label>
                        <flux:input wire:model="first_name" />
                        <flux:error name="first_name" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.last_name') }} *</flux:label>
                        <flux:input wire:model="last_name" />
                        <flux:error name="last_name" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.document_number') }} *</flux:label>
                        <flux:input wire:model="document_number" />
                        <flux:error name="document_number" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.birth_date') }}</flux:label>
                        <flux:input type="date" wire:model="birth_date" />
                        <flux:error name="birth_date" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.gender') }}</flux:label>
                        <flux:select wire:model="gender" placeholder="Seleccionar género">
                            <flux:option value="M">{{ __('voter_captures.male') }}</flux:option>
                            <flux:option value="F">{{ __('voter_captures.female') }}</flux:option>
                            <flux:option value="O">{{ __('voter_captures.other') }}</flux:option>
                        </flux:select>
                        <flux:error name="gender" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.contact_information') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.phone') }}</flux:label>
                        <flux:input wire:model="phone" />
                        <flux:error name="phone" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.secondary_phone') }}</flux:label>
                        <flux:input wire:model="secondary_phone" />
                        <flux:error name="secondary_phone" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.email') }}</flux:label>
                        <flux:input type="email" wire:model="email" />
                        <flux:error name="email" />
                    </flux:field>
                </div>

                <div class="md:col-span-2">
                    <flux:field>
                        <flux:label>{{ __('voter_captures.address') }}</flux:label>
                        <flux:textarea wire:model="address" rows="3" />
                        <flux:error name="address" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Location Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.location') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.state') }}</flux:label>
                        <flux:select wire:model.live="state_id" placeholder="{{ __('voter_captures.select_state') }}">
                            @foreach($states as $state)
                                <flux:option value="{{ $state->id }}">{{ $state->name }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="state_id" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.municipality') }}</flux:label>
                        <flux:select wire:model.live="municipality_id" placeholder="{{ __('voter_captures.select_municipality') }}" :disabled="!$state_id">
                            @foreach($municipalities as $municipality)
                                <flux:option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="municipality_id" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.parish') }}</flux:label>
                        <flux:select wire:model.live="parish_id" placeholder="{{ __('voter_captures.select_parish') }}" :disabled="!$municipality_id">
                            @foreach($parishes as $parish)
                                <flux:option value="{{ $parish->id }}">{{ $parish->name }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="parish_id" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Electoral Information Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.electoral_information') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.voting_center') }}</flux:label>
                        <flux:select wire:model="voting_center_id" placeholder="{{ __('voter_captures.select_voting_center') }}" :disabled="!$parish_id">
                            @foreach($votingCenters as $center)
                                <flux:option value="{{ $center->id }}">{{ $center->name }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="voting_center_id" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.voting_table') }}</flux:label>
                        <flux:input wire:model="voting_table" />
                        <flux:error name="voting_table" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Capture Management Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.capture_management') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.responsible') }} *</flux:label>
                        <flux:select wire:model="responsible_id" placeholder="{{ __('voter_captures.select_responsible') }}">
                            @foreach($responsibles as $responsible)
                                <flux:option value="{{ $responsible->id }}">{{ $responsible->full_name }}</flux:option>
                            @endforeach
                        </flux:select>
                        <flux:error name="responsible_id" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.capture_status') }} *</flux:label>
                        <flux:select wire:model="capture_status">
                            <flux:option value="pending">{{ __('voter_captures.pending') }}</flux:option>
                            <flux:option value="contacted">{{ __('voter_captures.contacted') }}</flux:option>
                            <flux:option value="confirmed">{{ __('voter_captures.confirmed') }}</flux:option>
                            <flux:option value="not_interested">{{ __('voter_captures.not_interested') }}</flux:option>
                            <flux:option value="unreachable">{{ __('voter_captures.unreachable') }}</flux:option>
                        </flux:select>
                        <flux:error name="capture_status" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.capture_date') }} *</flux:label>
                        <flux:input type="date" wire:model="capture_date" />
                        <flux:error name="capture_date" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.next_contact_date') }}</flux:label>
                        <flux:input type="date" wire:model="next_contact_date" />
                        <flux:error name="next_contact_date" />
                    </flux:field>
                </div>

                <div class="md:col-span-2">
                    <flux:field>
                        <flux:label>{{ __('voter_captures.contact_notes') }}</flux:label>
                        <flux:textarea wire:model="contact_notes" rows="3" />
                        <flux:error name="contact_notes" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Capture Details Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.capture_details') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.capture_source') }} *</flux:label>
                        <flux:select wire:model="capture_source">
                            <flux:option value="door_to_door">{{ __('voter_captures.door_to_door') }}</flux:option>
                            <flux:option value="phone_call">{{ __('voter_captures.phone_call') }}</flux:option>
                            <flux:option value="social_media">{{ __('voter_captures.social_media') }}</flux:option>
                            <flux:option value="event">{{ __('voter_captures.event') }}</flux:option>
                            <flux:option value="referral">{{ __('voter_captures.referral') }}</flux:option>
                            <flux:option value="other">{{ __('voter_captures.other_source') }}</flux:option>
                        </flux:select>
                        <flux:error name="capture_source" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.contact_method') }} *</flux:label>
                        <flux:select wire:model="contact_method">
                            <flux:option value="phone">{{ __('voter_captures.phone_contact') }}</flux:option>
                            <flux:option value="whatsapp">{{ __('voter_captures.whatsapp') }}</flux:option>
                            <flux:option value="visit">{{ __('voter_captures.visit') }}</flux:option>
                            <flux:option value="email">{{ __('voter_captures.email_contact') }}</flux:option>
                            <flux:option value="social_media">{{ __('voter_captures.social_media_contact') }}</flux:option>
                        </flux:select>
                        <flux:error name="contact_method" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.contact_attempts') }}</flux:label>
                        <flux:input type="number" wire:model="contact_attempts" min="0" />
                        <flux:error name="contact_attempts" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Interest and Commitment Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.interest_commitment') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.interest_level') }}</flux:label>
                        <flux:select wire:model="interest_level" placeholder="Seleccionar nivel">
                            <flux:option value="high">{{ __('voter_captures.high_interest') }}</flux:option>
                            <flux:option value="medium">{{ __('voter_captures.medium_interest') }}</flux:option>
                            <flux:option value="low">{{ __('voter_captures.low_interest') }}</flux:option>
                            <flux:option value="none">{{ __('voter_captures.no_interest') }}</flux:option>
                        </flux:select>
                        <flux:error name="interest_level" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.commitment_level') }}</flux:label>
                        <flux:select wire:model="commitment_level" placeholder="Seleccionar nivel">
                            <flux:option value="committed">{{ __('voter_captures.committed') }}</flux:option>
                            <flux:option value="likely">{{ __('voter_captures.likely') }}</flux:option>
                            <flux:option value="uncertain">{{ __('voter_captures.uncertain') }}</flux:option>
                            <flux:option value="unlikely">{{ __('voter_captures.unlikely') }}</flux:option>
                        </flux:select>
                        <flux:error name="commitment_level" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:checkbox wire:model="willing_to_vote">{{ __('voter_captures.willing_to_vote') }}</flux:checkbox>
                        <flux:error name="willing_to_vote" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:checkbox wire:model="willing_to_mobilize">{{ __('voter_captures.willing_to_mobilize') }}</flux:checkbox>
                        <flux:error name="willing_to_mobilize" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Follow-up Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.follow_up') }}</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:field>
                        <flux:checkbox wire:model="needs_follow_up">{{ __('voter_captures.needs_follow_up_field') }}</flux:checkbox>
                        <flux:error name="needs_follow_up" />
                    </flux:field>
                </div>

                <div>
                    <flux:field>
                        <flux:label>{{ __('voter_captures.follow_up_date') }}</flux:label>
                        <flux:input type="date" wire:model="follow_up_date" />
                        <flux:error name="follow_up_date" />
                    </flux:field>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.notes') }}</h3>
            
            <div>
                <flux:field>
                    <flux:textarea wire:model="notes" rows="4" placeholder="Notas adicionales sobre la captación..." />
                    <flux:error name="notes" />
                </flux:field>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end space-x-4">
            <flux:button href="{{ route('admin.voter-captures.index') }}" variant="ghost">
                {{ __('global.cancel') }}
            </flux:button>
            <flux:button type="submit" variant="primary">
                {{ __('voter_captures.add_capture') }}
            </flux:button>
        </div>
    </form>
</section>

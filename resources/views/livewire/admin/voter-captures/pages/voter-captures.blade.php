<section class="w-full">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item>{{ __('voter_captures.title') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <x-page-heading>
        <x-slot:title>{{ __('voter_captures.title') }}</x-slot:title>
        <x-slot:subtitle>{{ __('voter_captures.title_description') }}</x-slot:subtitle>
        <x-slot:buttons>
            @can('create captures')
                <flux:button href="{{ route('admin.voter-captures.create') }}" variant="primary" icon="plus">
                    {{ __('voter_captures.add_capture') }}
                </flux:button>
            @endcan
        </x-slot:buttons>
    </x-page-heading>

    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <!-- Search -->
            <div>
                <flux:input 
                    wire:model.live.debounce.300ms="search" 
                    placeholder="{{ __('voter_captures.search_placeholder') }}"
                    icon="magnifying-glass"
                />
            </div>

            <!-- Status Filter -->
            <div>
                <flux:select wire:model.live="statusFilter" placeholder="{{ __('voter_captures.filter_by_status') }}">
                    <flux:option value="">{{ __('voter_captures.all') }}</flux:option>
                    <flux:option value="pending">{{ __('voter_captures.pending') }}</flux:option>
                    <flux:option value="contacted">{{ __('voter_captures.contacted') }}</flux:option>
                    <flux:option value="confirmed">{{ __('voter_captures.confirmed') }}</flux:option>
                    <flux:option value="not_interested">{{ __('voter_captures.not_interested') }}</flux:option>
                    <flux:option value="unreachable">{{ __('voter_captures.unreachable') }}</flux:option>
                </flux:select>
            </div>

            <!-- Responsible Filter -->
            <div>
                <flux:select wire:model.live="responsibleFilter" placeholder="{{ __('voter_captures.filter_by_responsible') }}">
                    <flux:option value="">{{ __('voter_captures.select_responsible') }}</flux:option>
                    @foreach($responsibles as $responsible)
                        <flux:option value="{{ $responsible->id }}">{{ $responsible->full_name }}</flux:option>
                    @endforeach
                </flux:select>
            </div>

            <!-- State Filter -->
            <div>
                <flux:select wire:model.live="stateFilter" placeholder="{{ __('voter_captures.filter_by_location') }}">
                    <flux:option value="">{{ __('voter_captures.select_state') }}</flux:option>
                    @foreach($states as $state)
                        <flux:option value="{{ $state->id }}">{{ $state->name }}</flux:option>
                    @endforeach
                </flux:select>
            </div>
        </div>

        @if($stateFilter)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <!-- Municipality Filter -->
                <div>
                    <flux:select wire:model.live="municipalityFilter" placeholder="{{ __('voter_captures.select_municipality') }}">
                        <flux:option value="">{{ __('voter_captures.select_municipality') }}</flux:option>
                        @foreach($municipalities as $municipality)
                            <flux:option value="{{ $municipality->id }}">{{ $municipality->name }}</flux:option>
                        @endforeach
                    </flux:select>
                </div>

                @if($municipalityFilter)
                    <!-- Parish Filter -->
                    <div>
                        <flux:select wire:model.live="parishFilter" placeholder="{{ __('voter_captures.select_parish') }}">
                            <flux:option value="">{{ __('voter_captures.select_parish') }}</flux:option>
                            @foreach($parishes as $parish)
                                <flux:option value="{{ $parish->id }}">{{ $parish->name }}</flux:option>
                            @endforeach
                        </flux:select>
                    </div>
                @endif
            </div>
        @endif

        <!-- Clear Filters Button -->
        @if($search || $statusFilter || $responsibleFilter || $stateFilter)
            <div class="flex justify-end">
                <flux:button wire:click="clearFilters" variant="ghost" size="sm">
                    {{ __('voter_captures.clear_filters') }}
                </flux:button>
            </div>
        @endif
    </div>

    <!-- Tabs Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button 
                    wire:click="setActiveTab('all')"
                    class="@if($activeTab === 'all') border-blue-500 text-blue-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    {{ __('voter_captures.all') }} ({{ $tabCounts['all'] }})
                </button>
                <button 
                    wire:click="setActiveTab('pending')"
                    class="@if($activeTab === 'pending') border-blue-500 text-blue-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    {{ __('voter_captures.pending') }} ({{ $tabCounts['pending'] }})
                </button>
                <button 
                    wire:click="setActiveTab('contacted')"
                    class="@if($activeTab === 'contacted') border-blue-500 text-blue-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    {{ __('voter_captures.contacted') }} ({{ $tabCounts['contacted'] }})
                </button>
                <button 
                    wire:click="setActiveTab('confirmed')"
                    class="@if($activeTab === 'confirmed') border-blue-500 text-blue-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    {{ __('voter_captures.confirmed') }} ({{ $tabCounts['confirmed'] }})
                </button>
                <button 
                    wire:click="setActiveTab('overdue')"
                    class="@if($activeTab === 'overdue') border-red-500 text-red-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    {{ __('voter_captures.overdue') }} ({{ $tabCounts['overdue'] }})
                </button>
            </nav>
        </div>
    </div>

    <!-- Table Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <flux:table>
            <flux:columns>
                <flux:column>{{ __('voter_captures.first_name') }}</flux:column>
                <flux:column>{{ __('voter_captures.document_number') }}</flux:column>
                <flux:column>{{ __('voter_captures.phone') }}</flux:column>
                <flux:column>{{ __('voter_captures.responsible') }}</flux:column>
                <flux:column>{{ __('voter_captures.capture_status') }}</flux:column>
                <flux:column>{{ __('voter_captures.capture_date') }}</flux:column>
                <flux:column>{{ __('voter_captures.next_contact_date') }}</flux:column>
                <flux:column>{{ __('voter_captures.actions') }}</flux:column>
            </flux:columns>

            <flux:rows>
                @forelse($captures as $capture)
                    <flux:row>
                        <flux:cell>
                            <div>
                                <div class="font-medium text-gray-900">{{ $capture->full_name }}</div>
                                @if($capture->email)
                                    <div class="text-sm text-gray-500">{{ $capture->email }}</div>
                                @endif
                            </div>
                        </flux:cell>
                        <flux:cell>{{ $capture->document_number }}</flux:cell>
                        <flux:cell>{{ $capture->phone }}</flux:cell>
                        <flux:cell>{{ $capture->responsible->full_name }}</flux:cell>
                        <flux:cell>
                            <flux:badge 
                                size="sm" 
                                color="@switch($capture->capture_status)
                                    @case('pending') yellow @break
                                    @case('contacted') blue @break
                                    @case('confirmed') green @break
                                    @case('not_interested') red @break
                                    @case('unreachable') gray @break
                                    @default gray
                                @endswitch"
                            >
                                {{ __('voter_captures.' . $capture->capture_status) }}
                            </flux:badge>
                        </flux:cell>
                        <flux:cell>{{ $capture->capture_date->format('d/m/Y') }}</flux:cell>
                        <flux:cell>
                            @if($capture->next_contact_date)
                                <span class="@if($capture->isOverdue()) text-red-600 font-medium @endif">
                                    {{ $capture->next_contact_date->format('d/m/Y') }}
                                </span>
                            @else
                                <span class="text-gray-400">-</span>
                            @endif
                        </flux:cell>
                        <flux:cell>
                            <div class="flex items-center space-x-2">
                                @can('view captures')
                                    <flux:button 
                                        href="{{ route('admin.voter-captures.show', $capture) }}" 
                                        size="sm" 
                                        variant="ghost"
                                        icon="eye"
                                    >
                                    </flux:button>
                                @endcan

                                @can('update captures')
                                    <flux:button 
                                        href="{{ route('admin.voter-captures.edit', $capture) }}" 
                                        size="sm" 
                                        variant="ghost"
                                        icon="pencil"
                                    >
                                    </flux:button>

                                    @if($capture->capture_status === 'pending')
                                        <flux:button 
                                            wire:click="markAsContacted({{ $capture->id }})"
                                            wire:confirm="{{ __('voter_captures.confirm_mark_contacted') }}"
                                            size="sm" 
                                            variant="ghost"
                                            icon="phone"
                                        >
                                        </flux:button>
                                    @endif

                                    @if(in_array($capture->capture_status, ['pending', 'contacted']))
                                        <flux:button 
                                            wire:click="markAsConfirmed({{ $capture->id }})"
                                            wire:confirm="{{ __('voter_captures.confirm_mark_confirmed') }}"
                                            size="sm" 
                                            variant="ghost"
                                            icon="check"
                                        >
                                        </flux:button>
                                    @endif

                                    @if($capture->capture_status === 'confirmed' && !$capture->converted_to_person)
                                        @can('create people')
                                            <flux:button 
                                                wire:click="convertToPerson({{ $capture->id }})"
                                                wire:confirm="{{ __('voter_captures.confirm_convert') }}"
                                                size="sm" 
                                                variant="ghost"
                                                icon="arrow-right"
                                            >
                                            </flux:button>
                                        @endcan
                                    @endif
                                @endcan

                                @can('delete captures')
                                    <flux:button 
                                        wire:click="deleteCapture({{ $capture->id }})"
                                        wire:confirm="{{ __('voter_captures.confirm_delete') }}"
                                        size="sm" 
                                        variant="ghost"
                                        icon="trash"
                                    >
                                    </flux:button>
                                @endcan
                            </div>
                        </flux:cell>
                    </flux:row>
                @empty
                    <flux:row>
                        <flux:cell colspan="8" class="text-center py-8 text-gray-500">
                            {{ __('No captures found') }}
                        </flux:cell>
                    </flux:row>
                @endforelse
            </flux:rows>
        </flux:table>

        <!-- Pagination -->
        @if($captures->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $captures->links() }}
            </div>
        @endif
    </div>
</section>

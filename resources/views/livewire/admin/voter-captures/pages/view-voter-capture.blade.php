<section class="w-full">
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.voter-captures.index') }}">{{ __('voter_captures.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ $voterCapture->full_name }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <x-page-heading>
        <x-slot:title>{{ $voterCapture->full_name }}</x-slot:title>
        <x-slot:subtitle>{{ __('voter_captures.view_capture') }} - {{ $voterCapture->document_number }}</x-slot:subtitle>
        <x-slot:buttons>
            <div class="flex space-x-2">
                @can('update captures')
                    <flux:button href="{{ route('admin.voter-captures.edit', $voterCapture) }}" variant="primary" icon="pencil">
                        {{ __('voter_captures.edit') }}
                    </flux:button>

                    @if($voterCapture->capture_status === 'pending')
                        <flux:button 
                            wire:click="markAsContacted"
                            wire:confirm="{{ __('voter_captures.confirm_mark_contacted') }}"
                            variant="outline" 
                            icon="phone"
                        >
                            {{ __('voter_captures.mark_as_contacted') }}
                        </flux:button>
                    @endif

                    @if(in_array($voterCapture->capture_status, ['pending', 'contacted']))
                        <flux:button 
                            wire:click="markAsConfirmed"
                            wire:confirm="{{ __('voter_captures.confirm_mark_confirmed') }}"
                            variant="outline" 
                            icon="check"
                        >
                            {{ __('voter_captures.mark_as_confirmed') }}
                        </flux:button>
                    @endif
                @endcan

                @can('send capture reminders')
                    @if($voterCapture->needs_follow_up && !$voterCapture->converted_to_person)
                        <flux:button 
                            wire:click="sendReminder"
                            variant="outline" 
                            icon="bell"
                        >
                            {{ __('voter_captures.send_reminder') }}
                        </flux:button>
                    @endif
                @endcan

                @can('convert captures to people')
                    @if($voterCapture->capture_status === 'confirmed' && !$voterCapture->converted_to_person)
                        <flux:button 
                            wire:click="convertToPerson"
                            wire:confirm="{{ __('voter_captures.confirm_convert') }}"
                            variant="outline" 
                            icon="arrow-right"
                        >
                            {{ __('voter_captures.convert_to_person') }}
                        </flux:button>
                    @endif
                @endcan

                @can('delete captures')
                    <flux:button 
                        wire:click="deleteCapture"
                        wire:confirm="{{ __('voter_captures.confirm_delete') }}"
                        variant="danger" 
                        icon="trash"
                    >
                        {{ __('voter_captures.delete') }}
                    </flux:button>
                @endcan
            </div>
        </x-slot:buttons>
    </x-page-heading>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Data -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.personal_data') }}</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.first_name') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->first_name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.last_name') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->last_name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.document_number') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->document_number }}</p>
                    </div>
                    @if($voterCapture->birth_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.birth_date') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->birth_date->format('d/m/Y') }} ({{ $voterCapture->age }} años)</p>
                        </div>
                    @endif
                    @if($voterCapture->gender)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.gender') }}</label>
                            <p class="mt-1 text-sm text-gray-900">
                                @switch($voterCapture->gender)
                                    @case('M') {{ __('voter_captures.male') }} @break
                                    @case('F') {{ __('voter_captures.female') }} @break
                                    @case('O') {{ __('voter_captures.other') }} @break
                                @endswitch
                            </p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.contact_information') }}</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @if($voterCapture->phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.phone') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->phone }}</p>
                        </div>
                    @endif
                    @if($voterCapture->secondary_phone)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.secondary_phone') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->secondary_phone }}</p>
                        </div>
                    @endif
                    @if($voterCapture->email)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.email') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->email }}</p>
                        </div>
                    @endif
                    @if($voterCapture->address)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.address') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->address }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Location -->
            @if($voterCapture->state || $voterCapture->municipality || $voterCapture->parish)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.location') }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        @if($voterCapture->state)
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.state') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->state->name }}</p>
                            </div>
                        @endif
                        @if($voterCapture->municipality)
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.municipality') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->municipality->name }}</p>
                            </div>
                        @endif
                        @if($voterCapture->parish)
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.parish') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->parish->name }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Electoral Information -->
            @if($voterCapture->votingCenter || $voterCapture->voting_table)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.electoral_information') }}</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if($voterCapture->votingCenter)
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.voting_center') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->votingCenter->name }}</p>
                            </div>
                        @endif
                        @if($voterCapture->voting_table)
                            <div>
                                <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.voting_table') }}</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->voting_table }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            @endif

            <!-- Contact Notes -->
            @if($voterCapture->contact_notes || $voterCapture->notes)
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.notes') }}</h3>
                    
                    @if($voterCapture->contact_notes)
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.contact_notes') }}</label>
                            <div class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ $voterCapture->contact_notes }}</div>
                        </div>
                    @endif
                    
                    @if($voterCapture->notes)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.notes') }}</label>
                            <div class="mt-1 text-sm text-gray-900 whitespace-pre-wrap">{{ $voterCapture->notes }}</div>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Status Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.status') }}</h3>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.capture_status') }}</label>
                        <flux:badge size="sm" :color="$this->statusColor">
                            {{ __('voter_captures.' . $voterCapture->capture_status) }}
                        </flux:badge>
                    </div>
                    
                    @if($voterCapture->interest_level)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.interest_level') }}</label>
                            <flux:badge size="sm" :color="$this->interestColor">
                                {{ __('voter_captures.' . $voterCapture->interest_level . '_interest') }}
                            </flux:badge>
                        </div>
                    @endif
                    
                    @if($voterCapture->commitment_level)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.commitment_level') }}</label>
                            <flux:badge size="sm" :color="$this->commitmentColor">
                                {{ __('voter_captures.' . $voterCapture->commitment_level) }}
                            </flux:badge>
                        </div>
                    @endif

                    <div class="flex space-x-4">
                        @if($voterCapture->willing_to_vote)
                            <flux:badge size="sm" color="green">{{ __('voter_captures.willing_to_vote') }}</flux:badge>
                        @endif
                        @if($voterCapture->willing_to_mobilize)
                            <flux:badge size="sm" color="blue">{{ __('voter_captures.willing_to_mobilize') }}</flux:badge>
                        @endif
                    </div>

                    @if($voterCapture->converted_to_person)
                        <div>
                            <flux:badge size="sm" color="purple">{{ __('voter_captures.converted_to_person') }}</flux:badge>
                            @if($voterCapture->conversion_date)
                                <p class="text-xs text-gray-500 mt-1">{{ $voterCapture->conversion_date->format('d/m/Y H:i') }}</p>
                            @endif
                        </div>
                    @endif
                </div>
            </div>

            <!-- Capture Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.capture_details') }}</h3>
                
                <div class="space-y-3">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.responsible') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->responsible->full_name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.capture_date') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->capture_date->format('d/m/Y') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.capture_source') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ __('voter_captures.' . $voterCapture->capture_source) }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.contact_method') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ __('voter_captures.' . $voterCapture->contact_method . '_contact') }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.contact_attempts') }}</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->contact_attempts }}</p>
                    </div>
                </div>
            </div>

            <!-- Follow-up Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.follow_up') }}</h3>
                
                <div class="space-y-3">
                    @if($voterCapture->last_contact_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.last_contact_date') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->last_contact_date->format('d/m/Y') }}</p>
                        </div>
                    @endif
                    
                    @if($voterCapture->next_contact_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.next_contact_date') }}</label>
                            <p class="mt-1 text-sm @if($voterCapture->isOverdue()) text-red-600 font-medium @else text-gray-900 @endif">
                                {{ $voterCapture->next_contact_date->format('d/m/Y') }}
                                @if($voterCapture->isOverdue())
                                    ({{ __('voter_captures.overdue') }})
                                @endif
                            </p>
                        </div>
                    @endif
                    
                    @if($voterCapture->follow_up_date)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.follow_up_date') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->follow_up_date->format('d/m/Y') }}</p>
                        </div>
                    @endif
                    
                    @if($voterCapture->last_reminder_sent)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">{{ __('voter_captures.last_reminder_sent') }}</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $voterCapture->last_reminder_sent->format('d/m/Y H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</section>

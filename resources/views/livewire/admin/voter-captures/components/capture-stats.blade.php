<div class="space-y-6">
    <!-- Header with Refresh Button -->
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">{{ __('voter_captures.capture_stats') }}</h3>
        <flux:button wire:click="refreshStats" size="sm" variant="ghost" icon="arrow-path">
            {{ __('global.refresh') }}
        </flux:button>
    </div>

    <!-- Main Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Total Captures -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                        <flux:icon.users class="w-5 h-5 text-blue-600" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.total_captures') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['total_captures']) }}</p>
                </div>
            </div>
        </div>

        <!-- Pending Captures -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <flux:icon.clock class="w-5 h-5 text-yellow-600" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.pending_captures') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['pending_captures']) }}</p>
                </div>
            </div>
        </div>

        <!-- Confirmed Captures -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <flux:icon.check-circle class="w-5 h-5 text-green-600" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.confirmed_captures') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ number_format($stats['confirmed_captures']) }}</p>
                </div>
            </div>
        </div>

        <!-- Conversion Rate -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                        <flux:icon.arrow-trending-up class="w-5 h-5 text-purple-600" />
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.conversion_rate') }}</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $conversionRate }}%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Overdue Captures -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.overdue_captures') }}</p>
                    <p class="text-xl font-semibold text-red-600">{{ number_format($stats['overdue_captures']) }}</p>
                </div>
                <flux:icon.exclamation-triangle class="w-6 h-6 text-red-500" />
            </div>
        </div>

        <!-- This Month -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.captures_this_month') }}</p>
                    <p class="text-xl font-semibold text-blue-600">{{ number_format($stats['captures_this_month']) }}</p>
                </div>
                <flux:icon.calendar class="w-6 h-6 text-blue-500" />
            </div>
        </div>

        <!-- Needs Follow-up -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500">{{ __('voter_captures.needs_follow_up') }}</p>
                    <p class="text-xl font-semibold text-orange-600">{{ number_format($stats['needs_follow_up']) }}</p>
                </div>
                <flux:icon.bell class="w-6 h-6 text-orange-500" />
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Captures by Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.captures_by_status') }}</h4>
            
            <div class="space-y-3">
                @foreach($capturesByStatus as $statusData)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <flux:badge size="sm" :color="$statusData['color']">
                                {{ $statusData['label'] }}
                            </flux:badge>
                            <span class="text-sm text-gray-600">{{ $statusData['percentage'] }}%</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ number_format($statusData['count']) }}</span>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div 
                            class="h-2 rounded-full @switch($statusData['color'])
                                @case('yellow') bg-yellow-500 @break
                                @case('blue') bg-blue-500 @break
                                @case('green') bg-green-500 @break
                                @case('red') bg-red-500 @break
                                @case('gray') bg-gray-500 @break
                                @default bg-gray-500
                            @endswitch" 
                            style="width: {{ $statusData['percentage'] }}%"
                        ></div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Top Responsibles -->
        @if(!$showOnlyOwn && !empty($topResponsibles))
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">{{ __('voter_captures.captures_by_responsible') }}</h4>
                
                <div class="space-y-4">
                    @foreach($topResponsibles as $responsible)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-gray-900">{{ $responsible['responsible_name'] }}</p>
                                <div class="flex space-x-4 text-sm text-gray-600">
                                    <span>Total: {{ $responsible['total_captures'] }}</span>
                                    <span>Confirmados: {{ $responsible['confirmed_captures'] }}</span>
                                    <span>Convertidos: {{ $responsible['converted_captures'] }}</span>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-green-600">{{ $responsible['conversion_rate'] }}%</p>
                                <p class="text-xs text-gray-500">conversión</p>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Personal Performance (for individual users) -->
        @if($showOnlyOwn)
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">Mi Rendimiento</h4>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Tasa de Confirmación</span>
                        <span class="text-lg font-semibold text-blue-600">{{ $confirmationRate }}%</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Tasa de Conversión</span>
                        <span class="text-lg font-semibold text-green-600">{{ $conversionRate }}%</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Esta Semana</span>
                        <span class="text-lg font-semibold text-purple-600">{{ number_format($stats['captures_this_week']) }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Hoy</span>
                        <span class="text-lg font-semibold text-indigo-600">{{ number_format($stats['captures_today']) }}</span>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h4 class="text-lg font-medium text-gray-900 mb-4">{{ __('global.quick_actions') }}</h4>
        
        <div class="flex flex-wrap gap-3">
            @can('create captures')
                <flux:button href="{{ route('admin.voter-captures.create') }}" variant="primary" size="sm" icon="plus">
                    {{ __('voter_captures.add_capture') }}
                </flux:button>
            @endcan
            
            @can('view captures')
                <flux:button href="{{ route('admin.voter-captures.index') }}?activeTab=pending" variant="outline" size="sm" icon="clock">
                    Ver Pendientes ({{ $stats['pending_captures'] }})
                </flux:button>
            @endcan
            
            @if($stats['overdue_captures'] > 0)
                @can('view captures')
                    <flux:button href="{{ route('admin.voter-captures.index') }}?activeTab=overdue" variant="outline" size="sm" icon="exclamation-triangle">
                        Ver Vencidos ({{ $stats['overdue_captures'] }})
                    </flux:button>
                @endcan
            @endif
            
            @can('send capture reminders')
                @if($stats['needs_follow_up'] > 0)
                    <flux:button wire:click="$dispatch('send-bulk-reminders')" variant="outline" size="sm" icon="bell">
                        Enviar Recordatorios
                    </flux:button>
                @endif
            @endcan
        </div>
    </div>
</div>

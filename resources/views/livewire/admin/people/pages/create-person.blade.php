<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item href="{{ route('admin.people.index') }}">{{ __('people.title') }}</flux:breadcrumbs.item>
        <flux:breadcrumbs.item>{{ __('people.add_person') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <flux:heading size="xl">{{ __('people.add_person') }}</flux:heading>
    <flux:subheading>{{ __('people.title_description') }}</flux:subheading>

    <div class="mt-6">
        <flux:card>
            <div class="p-6">
                <p class="text-center text-zinc-500">
                    Create Person form will be implemented here.
                </p>
                
                <div class="mt-6 text-center">
                    <flux:button :href="route('admin.people.index')" variant="outline">
                        {{ __('global.back') }}
                    </flux:button>
                </div>
            </div>
        </flux:card>
    </div>
</div>

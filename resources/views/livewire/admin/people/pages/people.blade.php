<div>
    <flux:breadcrumbs>
        <flux:breadcrumbs.item href="{{ route('admin.index') }}" icon="home" />
        <flux:breadcrumbs.item>{{ __('people.title') }}</flux:breadcrumbs.item>
    </flux:breadcrumbs>

    <flux:heading size="xl">{{ __('people.title') }}</flux:heading>
    <flux:subheading>{{ __('people.title_description') }}</flux:subheading>

    <div class="mt-6">
        <flux:card>
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">{{ __('people.listing') }}</h2>
                
                <!-- Search and Filters -->
                <div class="mb-6 space-y-4">
                    <div class="flex gap-4">
                        <div class="flex-1">
                            <flux:input 
                                wire:model.live.debounce.300ms="search" 
                                placeholder="{{ __('people.search_by_name') }}"
                                icon="magnifying-glass"
                            />
                        </div>
                        <flux:button wire:click="clearFilters" variant="outline">
                            {{ __('people.clear_filters') }}
                        </flux:button>
                        <flux:button :href="route('admin.people.create')" variant="primary">
                            {{ __('people.add_person') }}
                        </flux:button>
                    </div>
                </div>

                <!-- Tabs -->
                <div class="mb-6">
                    <flux:tabs wire:model="activeTab">
                        <flux:tab name="all">{{ __('people.all') }} ({{ $stats['total'] }})</flux:tab>
                        <flux:tab name="militants">{{ __('people.militants') }} ({{ $stats['militants'] }})</flux:tab>
                        <flux:tab name="voters">{{ __('people.voters') }} ({{ $stats['voters'] }})</flux:tab>
                        <flux:tab name="sympathizers">{{ __('people.sympathizers') }} ({{ $stats['sympathizers'] }})</flux:tab>
                        <flux:tab name="leaders">{{ __('people.leaders_1x10') }} ({{ $stats['leaders'] }})</flux:tab>
                    </flux:tabs>
                </div>

                <!-- Results -->
                @if($people->count() > 0)
                    <div class="overflow-x-auto">
                        <flux:table :paginate="$people">
                            <flux:table.columns>
                                <flux:table.column sortable :sorted="$sortBy === 'name'" :direction="$sortDirection" wire:click="sort('name')">{{ __('people.full_name') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'document'" :direction="$sortDirection" wire:click="sort('document')">{{ __('people.document_number') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'type'" :direction="$sortDirection" wire:click="sort('type')">{{ __('people.person_type') }}</flux:table.column>
                                <flux:table.column sortable :sorted="$sortBy === 'status'" :direction="$sortDirection" wire:click="sort('status')">{{ __('people.status') }}</flux:table.column>
                                <flux:table.column>{{ __('people.location') }}</flux:table.column>
                                <flux:table.column>{{ __('global.actions') }}</flux:table.column>
                            </flux:table.columns>

                            <flux:table.rows>
                                @foreach($people as $person)
                                    <flux:table.row :key="$person->id">
                                        <flux:table.cell>
                                            <div>
                                                <div class="font-medium">{{ $person->full_name }}</div>
                                                @if($person->email)
                                                    <div class="text-sm text-zinc-500">{{ $person->email }}</div>
                                                @endif
                                            </div>
                                        </flux:table.cell>
                                        <flux:table.cell>{{ $person->cedula }}</flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" :color="match($person->tipo_persona) {
                                                'militante' => 'blue',
                                                'votante' => 'green',
                                                'simpatizante' => 'yellow',
                                                default => 'zinc'
                                            }">
                                                {{ ucfirst($person->tipo_persona) }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <flux:badge size="sm" :color="match($person->estado) {
                                                'activo' => 'green',
                                                'inactivo' => 'yellow',
                                                'suspendido' => 'red',
                                                default => 'zinc'
                                            }">
                                                {{ ucfirst($person->estado) }}
                                            </flux:badge>
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            @if($person->state)
                                                <div class="text-sm">
                                                    {{ $person->state->nombre }}
                                                    @if($person->municipality)
                                                        <br>{{ $person->municipality->nombre }}
                                                    @endif
                                                </div>
                                            @else
                                                <span class="text-zinc-400 text-sm">No location</span>
                                            @endif
                                        </flux:table.cell>
                                        <flux:table.cell>
                                            <div class="flex gap-2">
                                                <flux:button size="sm" :href="route('admin.people.show', $person)" variant="outline">
                                                    {{ __('people.view_details') }}
                                                </flux:button>
                                                <flux:button size="sm" :href="route('admin.people.edit', $person)" variant="outline">
                                                    {{ __('people.edit') }}
                                                </flux:button>
                                            </div>
                                        </flux:table.cell>
                                    </flux:table.row>
                                @endforeach
                            </flux:table.rows>
                        </flux:table>
                    </div>

                    <div class="mt-6">
                        {{ $people->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <flux:icon.users class="mx-auto h-12 w-12 text-zinc-400" />
                        <h3 class="mt-2 text-sm font-medium text-zinc-900 dark:text-zinc-100">{{ __('people.no_results') }}</h3>
                    </div>
                @endif
            </div>
        </flux:card>
    </div>
</div>

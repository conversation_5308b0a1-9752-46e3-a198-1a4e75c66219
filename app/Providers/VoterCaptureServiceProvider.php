<?php

namespace App\Providers;

use App\Events\VoterCaptureStatusChanged;
use App\Events\VoterCaptureConverted;
use App\Listeners\LogVoterCaptureStatusChange;
use App\Listeners\NotifyResponsibleOfStatusChange;
use App\Models\VoterCapture;
use App\Observers\VoterCaptureObserver;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\ServiceProvider;

class VoterCaptureServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register configuration
        $this->mergeConfigFrom(
            __DIR__.'/../../config/voter_captures.php', 'voter_captures'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register observers
        VoterCapture::observe(VoterCaptureObserver::class);

        // Register event listeners
        Event::listen(
            VoterCaptureStatusChanged::class,
            [LogVoterCaptureStatusChange::class, 'handle']
        );

        Event::listen(
            VoterCaptureStatusChanged::class,
            [NotifyResponsibleOfStatusChange::class, 'handle']
        );

        // Register custom validation rules
        $this->registerValidationRules();

        // Register custom blade directives
        $this->registerBladeDirectives();
    }

    /**
     * Register custom validation rules
     */
    private function registerValidationRules(): void
    {
        \Illuminate\Support\Facades\Validator::extend('venezuelan_document', function ($attribute, $value, $parameters, $validator) {
            $rule = new \App\Rules\VenezuelanDocumentNumber();
            $passes = true;
            
            $rule->validate($attribute, $value, function ($message) use (&$passes) {
                $passes = false;
            });
            
            return $passes;
        });
    }

    /**
     * Register custom blade directives
     */
    private function registerBladeDirectives(): void
    {
        \Illuminate\Support\Facades\Blade::directive('captureStatus', function ($expression) {
            return "<?php echo \App\Helpers\VoterCaptureHelper::getStatusBadge($expression); ?>";
        });

        \Illuminate\Support\Facades\Blade::directive('capturePriority', function ($expression) {
            return "<?php echo \App\Helpers\VoterCaptureHelper::getPriorityBadge($expression); ?>";
        });

        \Illuminate\Support\Facades\Blade::directive('captureQuality', function ($expression) {
            return "<?php echo \App\Helpers\VoterCaptureHelper::getQualityIndicator($expression); ?>";
        });
    }
}

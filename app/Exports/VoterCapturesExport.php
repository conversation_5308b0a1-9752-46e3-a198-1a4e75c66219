<?php

namespace App\Exports;

use App\Models\VoterCapture;
use Illuminate\Database\Eloquent\Builder;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class VoterCapturesExport implements FromQuery, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    public function __construct(
        private array $filters = [],
        private bool $includeArchived = false
    ) {}

    public function query(): Builder
    {
        $query = VoterCapture::query()
            ->with([
                'state:id,name',
                'municipality:id,name',
                'parish:id,name',
                'votingCenter:id,name',
                'responsible:id,first_name,last_name,document_number',
                'person:id,first_name,last_name',
                'createdBy:id,name',
                'updatedBy:id,name'
            ]);

        // Apply filters
        if (!empty($this->filters['search'])) {
            $query->search($this->filters['search']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('capture_status', $this->filters['status']);
        }

        if (!empty($this->filters['responsible_id'])) {
            $query->where('responsible_id', $this->filters['responsible_id']);
        }

        if (!empty($this->filters['state_id'])) {
            $query->where('state_id', $this->filters['state_id']);
        }

        if (!empty($this->filters['municipality_id'])) {
            $query->where('municipality_id', $this->filters['municipality_id']);
        }

        if (!empty($this->filters['parish_id'])) {
            $query->where('parish_id', $this->filters['parish_id']);
        }

        if (!empty($this->filters['priority'])) {
            $query->where('priority', $this->filters['priority']);
        }

        if (!empty($this->filters['campaign_id'])) {
            $query->where('campaign_id', $this->filters['campaign_id']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->where('capture_date', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->where('capture_date', '<=', $this->filters['date_to']);
        }

        if (!$this->includeArchived) {
            $query->where('status', '!=', 'archived');
        }

        return $query->orderBy('capture_date', 'desc');
    }

    public function headings(): array
    {
        return [
            'ID',
            'Nombres',
            'Apellidos',
            'Cédula',
            'Fecha Nacimiento',
            'Género',
            'Teléfono',
            'Teléfono Secundario',
            'Email',
            'Dirección',
            'Estado',
            'Municipio',
            'Parroquia',
            'Centro Votación',
            'Mesa',
            'Responsable',
            'Cédula Responsable',
            'Estado Captación',
            'Fecha Captación',
            'Último Contacto',
            'Próximo Contacto',
            'Fuente',
            'Detalles Fuente',
            'Método Contacto',
            'Intentos Contacto',
            'Nivel Interés',
            'Nivel Compromiso',
            'Dispuesto Votar',
            'Dispuesto Movilizar',
            'Requiere Seguimiento',
            'Fecha Seguimiento',
            'Prioridad',
            'Puntuación Calidad',
            'ID Campaña',
            'Código Referido',
            'Convertido',
            'Fecha Conversión',
            'Persona Convertida',
            'Estado',
            'Notas Contacto',
            'Notas',
            'Creado Por',
            'Actualizado Por',
            'Fecha Creación',
            'Fecha Actualización',
        ];
    }

    public function map($capture): array
    {
        return [
            $capture->id,
            $capture->first_name,
            $capture->last_name,
            $capture->document_number,
            $capture->birth_date?->format('d/m/Y'),
            match($capture->gender) {
                'M' => 'Masculino',
                'F' => 'Femenino',
                'O' => 'Otro',
                default => ''
            },
            $capture->phone,
            $capture->secondary_phone,
            $capture->email,
            $capture->address,
            $capture->state?->name,
            $capture->municipality?->name,
            $capture->parish?->name,
            $capture->votingCenter?->name,
            $capture->voting_table,
            $capture->responsible?->full_name,
            $capture->responsible?->document_number,
            $this->translateStatus($capture->capture_status),
            $capture->capture_date->format('d/m/Y'),
            $capture->last_contact_date?->format('d/m/Y'),
            $capture->next_contact_date?->format('d/m/Y'),
            $this->translateSource($capture->capture_source),
            $capture->source_details,
            $this->translateContactMethod($capture->contact_method),
            $capture->contact_attempts,
            $this->translateInterestLevel($capture->interest_level),
            $this->translateCommitmentLevel($capture->commitment_level),
            $capture->willing_to_vote ? 'Sí' : 'No',
            $capture->willing_to_mobilize ? 'Sí' : 'No',
            $capture->needs_follow_up ? 'Sí' : 'No',
            $capture->follow_up_date?->format('d/m/Y'),
            $this->translatePriority($capture->priority),
            $capture->quality_score,
            $capture->campaign_id,
            $capture->referral_code,
            $capture->converted_to_person ? 'Sí' : 'No',
            $capture->conversion_date?->format('d/m/Y H:i'),
            $capture->person?->full_name,
            $this->translateGeneralStatus($capture->status),
            $capture->contact_notes,
            $capture->notes,
            $capture->createdBy?->name,
            $capture->updatedBy?->name,
            $capture->created_at->format('d/m/Y H:i'),
            $capture->updated_at->format('d/m/Y H:i'),
        ];
    }

    public function styles(Worksheet $sheet): array
    {
        return [
            1 => [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E2E8F0']
                ]
            ],
        ];
    }

    private function translateStatus(string $status): string
    {
        return match($status) {
            'pending' => 'Pendiente',
            'contacted' => 'Contactado',
            'confirmed' => 'Confirmado',
            'not_interested' => 'No Interesado',
            'unreachable' => 'No Localizable',
            default => $status
        };
    }

    private function translateSource(string $source): string
    {
        return match($source) {
            'door_to_door' => 'Puerta a Puerta',
            'phone_call' => 'Llamada Telefónica',
            'social_media' => 'Redes Sociales',
            'event' => 'Evento',
            'referral' => 'Referido',
            'other' => 'Otro',
            default => $source
        };
    }

    private function translateContactMethod(string $method): string
    {
        return match($method) {
            'phone' => 'Teléfono',
            'whatsapp' => 'WhatsApp',
            'visit' => 'Visita',
            'email' => 'Email',
            'social_media' => 'Redes Sociales',
            default => $method
        };
    }

    private function translateInterestLevel(?string $level): string
    {
        if (!$level) return '';
        
        return match($level) {
            'high' => 'Alto',
            'medium' => 'Medio',
            'low' => 'Bajo',
            'none' => 'Ninguno',
            default => $level
        };
    }

    private function translateCommitmentLevel(?string $level): string
    {
        if (!$level) return '';
        
        return match($level) {
            'committed' => 'Comprometido',
            'likely' => 'Probable',
            'uncertain' => 'Incierto',
            'unlikely' => 'Improbable',
            default => $level
        };
    }

    private function translatePriority(?string $priority): string
    {
        if (!$priority) return '';
        
        return match($priority) {
            'low' => 'Baja',
            'medium' => 'Media',
            'high' => 'Alta',
            'urgent' => 'Urgente',
            default => $priority
        };
    }

    private function translateGeneralStatus(string $status): string
    {
        return match($status) {
            'active' => 'Activo',
            'inactive' => 'Inactivo',
            'archived' => 'Archivado',
            default => $status
        };
    }
}

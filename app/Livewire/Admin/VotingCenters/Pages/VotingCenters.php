<?php

namespace App\Livewire\Admin\VotingCenters\Pages;

use App\Models\VotingCenter;
use Illuminate\Contracts\View\View;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class VotingCenters extends Component
{
    use WithPagination;

    #[Url]
    public string $search = '';

    #[Url]
    public string $centerType = '';

    #[Url]
    public string $status = '';

    public string $sortBy = 'name';
    public string $sortDirection = 'asc';

    public function mount(): void
    {
        $this->authorize('view voting_centers');
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->centerType = '';
        $this->status = '';
        $this->resetPage();
    }

    public function sort(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $query = VotingCenter::query()
            ->with(['state', 'municipality', 'parish']);

        // Apply search filter
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('code', 'like', '%' . $this->search . '%')
                  ->orWhere('name', 'like', '%' . $this->search . '%')
                  ->orWhere('address', 'like', '%' . $this->search . '%');
            });
        }

        // Apply type filter
        if ($this->centerType) {
            $query->where('type', $this->centerType);
        }

        // Apply status filter
        if ($this->status) {
            $query->where('status', $this->status);
        }

        // Apply sorting
        $query->orderBy($this->sortBy, $this->sortDirection);

        $votingCenters = $query->paginate(15);

        return view('livewire.admin.voting-centers.pages.voting-centers', [
            'votingCenters' => $votingCenters,
        ]);
    }
}

<?php

namespace App\Livewire\Admin\VoterCaptures\Pages;

use App\Models\VoterCapture;
use App\Models\Person;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Contracts\View\View;
use Jan<PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;

class CreateVoterCapture extends Component
{
    use LivewireAlert;

    // Personal data
    #[Validate('required|string|max:255')]
    public string $first_name = '';

    #[Validate('required|string|max:255')]
    public string $last_name = '';

    #[Validate('required|string|max:20|unique:voter_captures,document_number')]
    public string $document_number = '';

    #[Validate('nullable|date')]
    public string $birth_date = '';

    #[Validate('nullable|in:M,F,O')]
    public string $gender = '';

    // Contact information
    #[Validate('nullable|string|max:20')]
    public string $phone = '';

    #[Validate('nullable|string|max:20')]
    public string $secondary_phone = '';

    #[Validate('nullable|email')]
    public string $email = '';

    #[Validate('nullable|string')]
    public string $address = '';

    // Location
    #[Validate('nullable|exists:states,id')]
    public string $state_id = '';

    #[Validate('nullable|exists:municipalities,id')]
    public string $municipality_id = '';

    #[Validate('nullable|exists:parishes,id')]
    public string $parish_id = '';

    // Electoral information
    #[Validate('nullable|exists:voting_centers,id')]
    public string $voting_center_id = '';

    #[Validate('nullable|string|max:10')]
    public string $voting_table = '';

    // Capture management
    #[Validate('required|exists:people,id')]
    public string $responsible_id = '';

    #[Validate('required|in:pending,contacted,confirmed,not_interested,unreachable')]
    public string $capture_status = 'pending';

    #[Validate('required|date')]
    public string $capture_date = '';

    #[Validate('nullable|date')]
    public string $last_contact_date = '';

    #[Validate('nullable|date')]
    public string $next_contact_date = '';

    #[Validate('nullable|string')]
    public string $contact_notes = '';

    // Capture details
    #[Validate('required|in:door_to_door,phone_call,social_media,event,referral,other')]
    public string $capture_source = 'door_to_door';

    #[Validate('required|in:phone,whatsapp,visit,email,social_media')]
    public string $contact_method = 'phone';

    #[Validate('nullable|integer|min:0')]
    public int $contact_attempts = 0;

    // Interest and commitment
    #[Validate('nullable|in:high,medium,low,none')]
    public string $interest_level = '';

    #[Validate('nullable|in:committed,likely,uncertain,unlikely')]
    public string $commitment_level = '';

    #[Validate('boolean')]
    public bool $willing_to_vote = false;

    #[Validate('boolean')]
    public bool $willing_to_mobilize = false;

    // Follow-up
    #[Validate('boolean')]
    public bool $needs_follow_up = true;

    #[Validate('nullable|date')]
    public string $follow_up_date = '';

    // Status and notes
    #[Validate('required|in:active,inactive,archived')]
    public string $status = 'active';

    #[Validate('nullable|string')]
    public string $notes = '';

    public function mount(): void
    {
        $this->authorize('create captures');
        $this->capture_date = now()->format('Y-m-d');
    }

    public function updatedStateId(): void
    {
        $this->municipality_id = '';
        $this->parish_id = '';
        $this->voting_center_id = '';
    }

    public function updatedMunicipalityId(): void
    {
        $this->parish_id = '';
        $this->voting_center_id = '';
    }

    public function updatedParishId(): void
    {
        $this->voting_center_id = '';
    }

    public function createCapture(): void
    {
        $this->authorize('create captures');

        $this->validate();

        try {
            VoterCapture::create([
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'document_number' => $this->document_number,
                'birth_date' => $this->birth_date ?: null,
                'gender' => $this->gender ?: null,
                'phone' => $this->phone ?: null,
                'secondary_phone' => $this->secondary_phone ?: null,
                'email' => $this->email ?: null,
                'address' => $this->address ?: null,
                'state_id' => $this->state_id ?: null,
                'municipality_id' => $this->municipality_id ?: null,
                'parish_id' => $this->parish_id ?: null,
                'voting_center_id' => $this->voting_center_id ?: null,
                'voting_table' => $this->voting_table ?: null,
                'responsible_id' => $this->responsible_id,
                'capture_status' => $this->capture_status,
                'capture_date' => $this->capture_date,
                'last_contact_date' => $this->last_contact_date ?: null,
                'next_contact_date' => $this->next_contact_date ?: null,
                'contact_notes' => $this->contact_notes ?: null,
                'capture_source' => $this->capture_source,
                'contact_method' => $this->contact_method,
                'contact_attempts' => $this->contact_attempts,
                'interest_level' => $this->interest_level ?: null,
                'commitment_level' => $this->commitment_level ?: null,
                'willing_to_vote' => $this->willing_to_vote,
                'willing_to_mobilize' => $this->willing_to_mobilize,
                'needs_follow_up' => $this->needs_follow_up,
                'follow_up_date' => $this->follow_up_date ?: null,
                'status' => $this->status,
                'notes' => $this->notes ?: null,
            ]);

            $this->flash('success', __('voter_captures.capture_created'));
            $this->redirect(route('admin.voter-captures.index'), navigate: true);

        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_creating_capture'));
        }
    }

    public function getStatesProperty()
    {
        return State::orderBy('name')->get();
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->state_id) {
            return collect();
        }

        return Municipality::where('state_id', $this->state_id)
                          ->orderBy('name')
                          ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipality_id) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipality_id)
                    ->orderBy('name')
                    ->get();
    }

    public function getVotingCentersProperty()
    {
        if (!$this->parish_id) {
            return collect();
        }

        return VotingCenter::where('parish_id', $this->parish_id)
                          ->orderBy('name')
                          ->get();
    }

    public function getResponsiblesProperty()
    {
        return Person::where('is_leader_1x10', true)
                    ->orderBy('first_name')
                    ->orderBy('last_name')
                    ->get();
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.voter-captures.pages.create-voter-capture', [
            'states' => $this->states,
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'votingCenters' => $this->votingCenters,
            'responsibles' => $this->responsibles,
        ]);
    }
}

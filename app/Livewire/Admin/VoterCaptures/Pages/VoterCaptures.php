<?php

namespace App\Livewire\Admin\VoterCaptures\Pages;

use App\Models\VoterCapture;
use App\Models\Person;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Contracts\View\View;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Session;
use Livewire\Attributes\Url;
use Livewire\Component;
use Livewire\WithPagination;

class VoterCaptures extends Component
{
    use LivewireAlert;
    use WithPagination;

    /** @var array<string,string> */
    protected $listeners = [
        'captureDeleted' => '$refresh',
        'captureCreated' => '$refresh',
        'captureUpdated' => '$refresh',
    ];

    #[Session]
    public int $perPage = 10;

    #[Url]
    public string $search = '';

    #[Url]
    public string $statusFilter = '';

    #[Url]
    public string $responsibleFilter = '';

    #[Url]
    public string $stateFilter = '';

    #[Url]
    public string $municipalityFilter = '';

    #[Url]
    public string $parishFilter = '';

    #[Url]
    public string $activeTab = 'all';

    public function mount(): void
    {
        $this->authorize('view captures');
    }

    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    public function updatingStatusFilter(): void
    {
        $this->resetPage();
    }

    public function updatingResponsibleFilter(): void
    {
        $this->resetPage();
    }

    public function updatingStateFilter(): void
    {
        $this->municipalityFilter = '';
        $this->parishFilter = '';
        $this->resetPage();
    }

    public function updatingMunicipalityFilter(): void
    {
        $this->parishFilter = '';
        $this->resetPage();
    }

    public function updatingParishFilter(): void
    {
        $this->resetPage();
    }

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
        $this->resetPage();
    }

    public function clearFilters(): void
    {
        $this->search = '';
        $this->statusFilter = '';
        $this->responsibleFilter = '';
        $this->stateFilter = '';
        $this->municipalityFilter = '';
        $this->parishFilter = '';
        $this->activeTab = 'all';
        $this->resetPage();
    }

    public function deleteCapture(int $captureId): void
    {
        $this->authorize('delete captures');

        $capture = VoterCapture::findOrFail($captureId);
        
        try {
            $capture->delete();
            $this->alert('success', __('voter_captures.capture_deleted'));
            $this->dispatch('captureDeleted');
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_deleting_capture'));
        }
    }

    public function markAsContacted(int $captureId): void
    {
        $this->authorize('update captures');

        $capture = VoterCapture::findOrFail($captureId);
        
        try {
            $capture->markAsContacted();
            $this->alert('success', __('voter_captures.capture_contacted'));
            $this->dispatch('captureUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_updating_capture'));
        }
    }

    public function markAsConfirmed(int $captureId): void
    {
        $this->authorize('update captures');

        $capture = VoterCapture::findOrFail($captureId);
        
        try {
            $capture->markAsConfirmed();
            $this->alert('success', __('voter_captures.capture_confirmed'));
            $this->dispatch('captureUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_updating_capture'));
        }
    }

    public function convertToPerson(int $captureId): void
    {
        $this->authorize('create people');

        $capture = VoterCapture::findOrFail($captureId);
        
        try {
            $person = $capture->convertToPerson();
            $this->alert('success', __('voter_captures.person_converted'));
            $this->dispatch('captureUpdated');
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_converting_person'));
        }
    }

    public function getStatesProperty()
    {
        return State::orderBy('name')->get();
    }

    public function getMunicipalitiesProperty()
    {
        if (!$this->stateFilter) {
            return collect();
        }

        return Municipality::where('state_id', $this->stateFilter)
                          ->orderBy('name')
                          ->get();
    }

    public function getParishesProperty()
    {
        if (!$this->municipalityFilter) {
            return collect();
        }

        return Parish::where('municipality_id', $this->municipalityFilter)
                    ->orderBy('name')
                    ->get();
    }

    public function getResponsiblesProperty()
    {
        return Person::where('is_leader_1x10', true)
                    ->orderBy('first_name')
                    ->orderBy('last_name')
                    ->get();
    }

    public function getTabCountsProperty()
    {
        $baseQuery = VoterCapture::query();

        // Apply current filters except status
        if ($this->search) {
            $baseQuery->search($this->search);
        }

        if ($this->responsibleFilter) {
            $baseQuery->where('responsible_id', $this->responsibleFilter);
        }

        if ($this->stateFilter) {
            $baseQuery->where('state_id', $this->stateFilter);
        }

        if ($this->municipalityFilter) {
            $baseQuery->where('municipality_id', $this->municipalityFilter);
        }

        if ($this->parishFilter) {
            $baseQuery->where('parish_id', $this->parishFilter);
        }

        return [
            'all' => (clone $baseQuery)->count(),
            'pending' => (clone $baseQuery)->where('capture_status', 'pending')->count(),
            'contacted' => (clone $baseQuery)->where('capture_status', 'contacted')->count(),
            'confirmed' => (clone $baseQuery)->where('capture_status', 'confirmed')->count(),
            'not_interested' => (clone $baseQuery)->where('capture_status', 'not_interested')->count(),
            'unreachable' => (clone $baseQuery)->where('capture_status', 'unreachable')->count(),
            'overdue' => (clone $baseQuery)->overdue()->count(),
        ];
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        $query = VoterCapture::query()
            ->with(['state', 'municipality', 'parish', 'votingCenter', 'responsible', 'person'])
            ->when($this->search, function ($query, $search) {
                $query->search($search);
            })
            ->when($this->statusFilter, function ($query, $status) {
                $query->where('capture_status', $status);
            })
            ->when($this->responsibleFilter, function ($query, $responsible) {
                $query->where('responsible_id', $responsible);
            })
            ->when($this->stateFilter, function ($query, $state) {
                $query->where('state_id', $state);
            })
            ->when($this->municipalityFilter, function ($query, $municipality) {
                $query->where('municipality_id', $municipality);
            })
            ->when($this->parishFilter, function ($query, $parish) {
                $query->where('parish_id', $parish);
            });

        // Apply tab filters
        switch ($this->activeTab) {
            case 'pending':
                $query->where('capture_status', 'pending');
                break;
            case 'contacted':
                $query->where('capture_status', 'contacted');
                break;
            case 'confirmed':
                $query->where('capture_status', 'confirmed');
                break;
            case 'not_interested':
                $query->where('capture_status', 'not_interested');
                break;
            case 'unreachable':
                $query->where('capture_status', 'unreachable');
                break;
            case 'overdue':
                $query->overdue();
                break;
        }

        $captures = $query->orderBy('created_at', 'desc')->paginate($this->perPage);

        return view('livewire.admin.voter-captures.pages.voter-captures', [
            'captures' => $captures,
            'states' => $this->states,
            'municipalities' => $this->municipalities,
            'parishes' => $this->parishes,
            'responsibles' => $this->responsibles,
            'tabCounts' => $this->tabCounts,
        ]);
    }
}

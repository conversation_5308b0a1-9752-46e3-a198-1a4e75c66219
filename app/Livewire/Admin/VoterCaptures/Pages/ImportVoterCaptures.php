<?php

namespace App\Livewire\Admin\VoterCaptures\Pages;

use App\Imports\VoterCapturesImport;
use Illuminate\Contracts\View\View;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\WithFileUploads;
use Maatwebsite\Excel\Facades\Excel;

class ImportVoterCaptures extends Component
{
    use LivewireAlert, WithFileUploads;

    #[Validate('required|file|mimes:csv,xlsx,xls|max:10240')] // 10MB max
    public $file;

    public bool $hasHeaders = true;
    public bool $updateExisting = false;
    public string $defaultResponsibleId = '';
    public string $defaultCampaignId = '';
    public string $defaultPriority = 'medium';

    public array $importResults = [];
    public bool $showResults = false;

    public function mount(): void
    {
        $this->authorize('import captures');
    }

    public function import(): void
    {
        $this->authorize('import captures');
        
        $this->validate();

        try {
            $import = new VoterCapturesImport(
                hasHeaders: $this->hasHeaders,
                updateExisting: $this->updateExisting,
                defaultResponsibleId: $this->defaultResponsibleId ?: null,
                defaultCampaignId: $this->defaultCampaignId ?: null,
                defaultPriority: $this->defaultPriority
            );

            Excel::import($import, $this->file->getRealPath());

            $this->importResults = [
                'total_rows' => $import->getTotalRows(),
                'successful_imports' => $import->getSuccessfulImports(),
                'failed_imports' => $import->getFailedImports(),
                'updated_records' => $import->getUpdatedRecords(),
                'errors' => $import->getErrors(),
            ];

            $this->showResults = true;

            if ($this->importResults['failed_imports'] === 0) {
                $this->alert('success', __('voter_captures.import_completed_successfully', [
                    'imported' => $this->importResults['successful_imports'],
                    'updated' => $this->importResults['updated_records']
                ]));
            } else {
                $this->alert('warning', __('voter_captures.import_completed_with_errors', [
                    'imported' => $this->importResults['successful_imports'],
                    'failed' => $this->importResults['failed_imports']
                ]));
            }

        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.import_failed') . ': ' . $e->getMessage());
        }
    }

    public function downloadTemplate(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $headers = [
            'first_name',
            'last_name', 
            'document_number',
            'birth_date',
            'gender',
            'phone',
            'secondary_phone',
            'email',
            'address',
            'state_name',
            'municipality_name',
            'parish_name',
            'voting_center_name',
            'voting_table',
            'responsible_document',
            'capture_source',
            'source_details',
            'contact_method',
            'interest_level',
            'commitment_level',
            'willing_to_vote',
            'willing_to_mobilize',
            'priority',
            'campaign_id',
            'referral_code',
            'notes'
        ];

        $filename = 'voter_captures_template.csv';
        $handle = fopen('php://temp', 'w+');
        
        fputcsv($handle, $headers);
        
        // Add sample row
        fputcsv($handle, [
            'Juan',
            'Pérez',
            '12345678',
            '1990-01-15',
            'M',
            '04121234567',
            '02121234567',
            '<EMAIL>',
            'Av. Principal, Casa 123',
            'Miranda',
            'Chacao',
            'Chacao',
            'Centro de Votación Ejemplo',
            '001',
            '87654321',
            'door_to_door',
            'Visita domiciliaria',
            'phone',
            'high',
            'committed',
            'true',
            'true',
            'high',
            'CAMP2024',
            'REF001',
            'Votante muy comprometido'
        ]);

        rewind($handle);
        $content = stream_get_contents($handle);
        fclose($handle);

        return response()->streamDownload(function () use ($content) {
            echo $content;
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ]);
    }

    public function resetImport(): void
    {
        $this->reset(['file', 'importResults', 'showResults']);
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.voter-captures.pages.import-voter-captures');
    }
}

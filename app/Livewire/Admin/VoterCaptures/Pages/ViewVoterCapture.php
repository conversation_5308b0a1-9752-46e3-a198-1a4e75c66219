<?php

namespace App\Livewire\Admin\VoterCaptures\Pages;

use App\Models\VoterCapture;
use App\Jobs\SendCaptureReminderJob;
use Illuminate\Contracts\View\View;
use <PERSON><PERSON>nerezo\LivewireAlert\LivewireAlert;
use Livewire\Attributes\Layout;
use Livewire\Component;

class ViewVoterCapture extends Component
{
    use LivewireAlert;

    public VoterCapture $voterCapture;

    public function mount(VoterCapture $voterCapture): void
    {
        $this->authorize('view captures');
        
        // Check if user can only view their own captures
        if (auth()->user()->can('view own captures') && !auth()->user()->can('view all captures')) {
            // Get the person associated with the current user
            $userPerson = auth()->user()->person;
            if (!$userPerson || $voterCapture->responsible_id !== $userPerson->id) {
                abort(403, 'You can only view your own captures.');
            }
        }

        $this->voterCapture = $voterCapture->load([
            'state',
            'municipality',
            'parish',
            'votingCenter',
            'responsible',
            'person',
        ]);
    }

    public function markAsContacted(): void
    {
        $this->authorize('update captures');

        try {
            $this->voterCapture->markAsContacted();
            $this->voterCapture->refresh();
            $this->alert('success', __('voter_captures.capture_contacted'));
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_updating_capture'));
        }
    }

    public function markAsConfirmed(): void
    {
        $this->authorize('update captures');

        try {
            $this->voterCapture->markAsConfirmed();
            $this->voterCapture->refresh();
            $this->alert('success', __('voter_captures.capture_confirmed'));
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_updating_capture'));
        }
    }

    public function convertToPerson(): void
    {
        $this->authorize('convert captures to people');

        try {
            $person = $this->voterCapture->convertToPerson();
            $this->voterCapture->refresh();
            $this->alert('success', __('voter_captures.person_converted'));
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_converting_person'));
        }
    }

    public function sendReminder(): void
    {
        $this->authorize('send capture reminders');

        try {
            SendCaptureReminderJob::dispatch($this->voterCapture);
            $this->alert('success', __('voter_captures.reminder_sent'));
        } catch (\Exception $e) {
            $this->alert('error', 'Error al enviar recordatorio');
        }
    }

    public function deleteCapture(): void
    {
        $this->authorize('delete captures');

        try {
            $this->voterCapture->delete();
            $this->flash('success', __('voter_captures.capture_deleted'));
            $this->redirect(route('admin.voter-captures.index'), navigate: true);
        } catch (\Exception $e) {
            $this->alert('error', __('voter_captures.error_deleting_capture'));
        }
    }

    public function getStatusColorProperty(): string
    {
        return match($this->voterCapture->capture_status) {
            'pending' => 'yellow',
            'contacted' => 'blue',
            'confirmed' => 'green',
            'not_interested' => 'red',
            'unreachable' => 'gray',
            default => 'gray'
        };
    }

    public function getInterestColorProperty(): string
    {
        return match($this->voterCapture->interest_level) {
            'high' => 'green',
            'medium' => 'yellow',
            'low' => 'orange',
            'none' => 'red',
            default => 'gray'
        };
    }

    public function getCommitmentColorProperty(): string
    {
        return match($this->voterCapture->commitment_level) {
            'committed' => 'green',
            'likely' => 'blue',
            'uncertain' => 'yellow',
            'unlikely' => 'red',
            default => 'gray'
        };
    }

    #[Layout('components.layouts.admin')]
    public function render(): View
    {
        return view('livewire.admin.voter-captures.pages.view-voter-capture');
    }
}

<?php

namespace App\Livewire\Admin\VoterCaptures\Components;

use App\Models\VoterCapture;
use App\Models\Person;
use Illuminate\Contracts\View\View;
use Livewire\Component;
use Illuminate\Support\Facades\Cache;

class CaptureStats extends Component
{
    public bool $showOnlyOwn = false;

    public function mount(): void
    {
        // Check if user can only view their own captures
        if (auth()->user()->can('view own captures') && !auth()->user()->can('view all captures')) {
            $this->showOnlyOwn = true;
        }
    }

    public function getStatsProperty(): array
    {
        $cacheKey = 'capture_stats_' . auth()->id() . '_' . ($this->showOnlyOwn ? 'own' : 'all');
        
        return Cache::remember($cacheKey, config('voter_captures.statistics.cache_duration', 3600), function () {
            $query = VoterCapture::query();

            // Filter by responsible if user can only see their own
            if ($this->showOnlyOwn) {
                $userPerson = auth()->user()->person;
                if ($userPerson) {
                    $query->where('responsible_id', $userPerson->id);
                }
            }

            $baseQuery = clone $query;

            return [
                'total_captures' => $baseQuery->count(),
                'pending_captures' => (clone $query)->where('capture_status', 'pending')->count(),
                'contacted_captures' => (clone $query)->where('capture_status', 'contacted')->count(),
                'confirmed_captures' => (clone $query)->where('capture_status', 'confirmed')->count(),
                'not_interested_captures' => (clone $query)->where('capture_status', 'not_interested')->count(),
                'unreachable_captures' => (clone $query)->where('capture_status', 'unreachable')->count(),
                'overdue_captures' => (clone $query)->overdue()->count(),
                'converted_captures' => (clone $query)->where('converted_to_person', true)->count(),
                'captures_this_month' => (clone $query)->whereMonth('capture_date', now()->month)
                                                        ->whereYear('capture_date', now()->year)
                                                        ->count(),
                'captures_this_week' => (clone $query)->whereBetween('capture_date', [
                    now()->startOfWeek(),
                    now()->endOfWeek()
                ])->count(),
                'captures_today' => (clone $query)->whereDate('capture_date', now())->count(),
                'needs_follow_up' => (clone $query)->where('needs_follow_up', true)
                                                   ->where('converted_to_person', false)
                                                   ->count(),
            ];
        });
    }

    public function getConversionRateProperty(): float
    {
        $stats = $this->stats;
        
        if ($stats['total_captures'] === 0) {
            return 0;
        }

        return round(($stats['converted_captures'] / $stats['total_captures']) * 100, 2);
    }

    public function getConfirmationRateProperty(): float
    {
        $stats = $this->stats;
        
        if ($stats['total_captures'] === 0) {
            return 0;
        }

        return round(($stats['confirmed_captures'] / $stats['total_captures']) * 100, 2);
    }

    public function getTopResponsiblesProperty(): array
    {
        $cacheKey = 'top_responsibles_' . auth()->id() . '_' . ($this->showOnlyOwn ? 'own' : 'all');
        
        return Cache::remember($cacheKey, config('voter_captures.statistics.cache_duration', 3600), function () {
            $query = VoterCapture::query()
                ->select('responsible_id')
                ->selectRaw('COUNT(*) as total_captures')
                ->selectRaw('SUM(CASE WHEN capture_status = "confirmed" THEN 1 ELSE 0 END) as confirmed_captures')
                ->selectRaw('SUM(CASE WHEN converted_to_person = 1 THEN 1 ELSE 0 END) as converted_captures')
                ->with('responsible:id,first_name,last_name')
                ->groupBy('responsible_id');

            // Filter by responsible if user can only see their own
            if ($this->showOnlyOwn) {
                $userPerson = auth()->user()->person;
                if ($userPerson) {
                    $query->where('responsible_id', $userPerson->id);
                }
            }

            return $query->orderByDesc('total_captures')
                        ->limit(5)
                        ->get()
                        ->map(function ($item) {
                            return [
                                'responsible_name' => $item->responsible->full_name ?? 'N/A',
                                'total_captures' => $item->total_captures,
                                'confirmed_captures' => $item->confirmed_captures,
                                'converted_captures' => $item->converted_captures,
                                'confirmation_rate' => $item->total_captures > 0 ? 
                                    round(($item->confirmed_captures / $item->total_captures) * 100, 1) : 0,
                                'conversion_rate' => $item->total_captures > 0 ? 
                                    round(($item->converted_captures / $item->total_captures) * 100, 1) : 0,
                            ];
                        })
                        ->toArray();
        });
    }

    public function getCapturesByStatusProperty(): array
    {
        $stats = $this->stats;
        
        return [
            [
                'status' => 'pending',
                'label' => __('voter_captures.pending'),
                'count' => $stats['pending_captures'],
                'color' => 'yellow',
                'percentage' => $stats['total_captures'] > 0 ? 
                    round(($stats['pending_captures'] / $stats['total_captures']) * 100, 1) : 0,
            ],
            [
                'status' => 'contacted',
                'label' => __('voter_captures.contacted'),
                'count' => $stats['contacted_captures'],
                'color' => 'blue',
                'percentage' => $stats['total_captures'] > 0 ? 
                    round(($stats['contacted_captures'] / $stats['total_captures']) * 100, 1) : 0,
            ],
            [
                'status' => 'confirmed',
                'label' => __('voter_captures.confirmed'),
                'count' => $stats['confirmed_captures'],
                'color' => 'green',
                'percentage' => $stats['total_captures'] > 0 ? 
                    round(($stats['confirmed_captures'] / $stats['total_captures']) * 100, 1) : 0,
            ],
            [
                'status' => 'not_interested',
                'label' => __('voter_captures.not_interested'),
                'count' => $stats['not_interested_captures'],
                'color' => 'red',
                'percentage' => $stats['total_captures'] > 0 ? 
                    round(($stats['not_interested_captures'] / $stats['total_captures']) * 100, 1) : 0,
            ],
            [
                'status' => 'unreachable',
                'label' => __('voter_captures.unreachable'),
                'count' => $stats['unreachable_captures'],
                'color' => 'gray',
                'percentage' => $stats['total_captures'] > 0 ? 
                    round(($stats['unreachable_captures'] / $stats['total_captures']) * 100, 1) : 0,
            ],
        ];
    }

    public function refreshStats(): void
    {
        $cacheKey = 'capture_stats_' . auth()->id() . '_' . ($this->showOnlyOwn ? 'own' : 'all');
        Cache::forget($cacheKey);
        
        $cacheKey = 'top_responsibles_' . auth()->id() . '_' . ($this->showOnlyOwn ? 'own' : 'all');
        Cache::forget($cacheKey);
        
        $this->dispatch('$refresh');
    }

    public function render(): View
    {
        return view('livewire.admin.voter-captures.components.capture-stats', [
            'stats' => $this->stats,
            'conversionRate' => $this->conversionRate,
            'confirmationRate' => $this->confirmationRate,
            'topResponsibles' => $this->topResponsibles,
            'capturesByStatus' => $this->capturesByStatus,
        ]);
    }
}

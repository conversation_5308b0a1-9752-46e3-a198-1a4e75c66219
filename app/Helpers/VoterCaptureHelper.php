<?php

namespace App\Helpers;

class VoterCaptureHelper
{
    /**
     * Get status badge HTML
     */
    public static function getStatusBadge(string $status): string
    {
        $config = config("voter_captures.statuses.{$status}", [
            'color' => 'gray',
            'icon' => 'question-mark-circle'
        ]);

        $label = __("voter_captures.{$status}");
        $color = $config['color'];

        return "<flux:badge size=\"sm\" color=\"{$color}\">{$label}</flux:badge>";
    }

    /**
     * Get priority badge HTML
     */
    public static function getPriorityBadge(string $priority): string
    {
        $colors = [
            'low' => 'gray',
            'medium' => 'blue',
            'high' => 'orange',
            'urgent' => 'red'
        ];

        $label = __("voter_captures.{$priority}_priority");
        $color = $colors[$priority] ?? 'gray';

        return "<flux:badge size=\"sm\" color=\"{$color}\">{$label}</flux:badge>";
    }

    /**
     * Get quality indicator HTML
     */
    public static function getQualityIndicator(?int $score): string
    {
        if ($score === null) {
            return '<span class="text-gray-400">-</span>';
        }

        $color = match(true) {
            $score >= 80 => 'text-green-600',
            $score >= 60 => 'text-blue-600',
            $score >= 40 => 'text-yellow-600',
            default => 'text-red-600'
        };

        $icon = match(true) {
            $score >= 80 => '★★★',
            $score >= 60 => '★★☆',
            $score >= 40 => '★☆☆',
            default => '☆☆☆'
        };

        return "<span class=\"{$color} font-medium\" title=\"Calidad: {$score}/100\">{$icon} {$score}</span>";
    }

    /**
     * Calculate days until next contact
     */
    public static function getDaysUntilContact(?\Carbon\Carbon $nextContactDate): ?int
    {
        if (!$nextContactDate) {
            return null;
        }

        return now()->diffInDays($nextContactDate, false);
    }

    /**
     * Get contact urgency level
     */
    public static function getContactUrgency(?\Carbon\Carbon $nextContactDate): string
    {
        if (!$nextContactDate) {
            return 'none';
        }

        $days = self::getDaysUntilContact($nextContactDate);

        return match(true) {
            $days < 0 => 'overdue',
            $days === 0 => 'today',
            $days === 1 => 'tomorrow',
            $days <= 3 => 'soon',
            default => 'normal'
        };
    }

    /**
     * Get urgency color class
     */
    public static function getUrgencyColor(string $urgency): string
    {
        return match($urgency) {
            'overdue' => 'text-red-600 font-bold',
            'today' => 'text-orange-600 font-medium',
            'tomorrow' => 'text-yellow-600',
            'soon' => 'text-blue-600',
            default => 'text-gray-600'
        };
    }

    /**
     * Format contact attempts with context
     */
    public static function formatContactAttempts(int $attempts): string
    {
        if ($attempts === 0) {
            return '<span class="text-gray-400">Sin intentos</span>';
        }

        $color = match(true) {
            $attempts >= 5 => 'text-red-600',
            $attempts >= 3 => 'text-yellow-600',
            default => 'text-green-600'
        };

        $plural = $attempts === 1 ? 'intento' : 'intentos';

        return "<span class=\"{$color}\">{$attempts} {$plural}</span>";
    }

    /**
     * Get conversion probability based on data
     */
    public static function getConversionProbability(
        string $captureStatus,
        ?string $interestLevel,
        ?string $commitmentLevel,
        int $contactAttempts,
        ?int $qualityScore
    ): array {
        $probability = 0;
        $factors = [];

        // Base probability by status
        $probability += match($captureStatus) {
            'confirmed' => 90,
            'contacted' => 40,
            'pending' => 20,
            'not_interested' => 5,
            'unreachable' => 2,
            default => 10
        };

        // Interest level factor
        if ($interestLevel) {
            $interestBonus = match($interestLevel) {
                'high' => 20,
                'medium' => 10,
                'low' => -5,
                'none' => -20,
                default => 0
            };
            $probability += $interestBonus;
            $factors[] = "Interés {$interestLevel}: {$interestBonus}%";
        }

        // Commitment level factor
        if ($commitmentLevel) {
            $commitmentBonus = match($commitmentLevel) {
                'committed' => 25,
                'likely' => 15,
                'uncertain' => 0,
                'unlikely' => -15,
                default => 0
            };
            $probability += $commitmentBonus;
            $factors[] = "Compromiso {$commitmentLevel}: {$commitmentBonus}%";
        }

        // Contact attempts factor (diminishing returns)
        if ($contactAttempts > 0) {
            $contactBonus = min($contactAttempts * 3, 15) - ($contactAttempts > 3 ? ($contactAttempts - 3) * 2 : 0);
            $probability += $contactBonus;
            $factors[] = "Contactos ({$contactAttempts}): {$contactBonus}%";
        }

        // Quality score factor
        if ($qualityScore !== null) {
            $qualityBonus = ($qualityScore - 50) / 5; // -10 to +10 range
            $probability += $qualityBonus;
            $factors[] = "Calidad ({$qualityScore}): " . number_format($qualityBonus, 1) . "%";
        }

        // Ensure probability is within bounds
        $probability = max(0, min(100, $probability));

        return [
            'probability' => round($probability),
            'factors' => $factors,
            'level' => match(true) {
                $probability >= 80 => 'very_high',
                $probability >= 60 => 'high',
                $probability >= 40 => 'medium',
                $probability >= 20 => 'low',
                default => 'very_low'
            }
        ];
    }

    /**
     * Generate next contact suggestions
     */
    public static function getNextContactSuggestions(
        string $captureStatus,
        ?string $contactMethod,
        int $contactAttempts,
        ?\Carbon\Carbon $lastContactDate
    ): array {
        $suggestions = [];

        // Base suggestions by status
        switch ($captureStatus) {
            case 'pending':
                $suggestions[] = [
                    'action' => 'Realizar primer contacto',
                    'method' => $contactMethod ?? 'phone',
                    'priority' => 'high',
                    'timeframe' => 'Hoy'
                ];
                break;

            case 'contacted':
                if ($contactAttempts < 3) {
                    $suggestions[] = [
                        'action' => 'Seguimiento de contacto',
                        'method' => 'whatsapp',
                        'priority' => 'medium',
                        'timeframe' => '2-3 días'
                    ];
                } else {
                    $suggestions[] = [
                        'action' => 'Cambiar método de contacto',
                        'method' => $contactMethod === 'phone' ? 'visit' : 'phone',
                        'priority' => 'medium',
                        'timeframe' => '1 semana'
                    ];
                }
                break;

            case 'confirmed':
                $suggestions[] = [
                    'action' => 'Convertir a persona',
                    'method' => 'system',
                    'priority' => 'high',
                    'timeframe' => 'Inmediato'
                ];
                break;
        }

        // Add time-based suggestions
        if ($lastContactDate && $lastContactDate->diffInDays(now()) > 7) {
            $suggestions[] = [
                'action' => 'Contacto de mantenimiento',
                'method' => 'whatsapp',
                'priority' => 'low',
                'timeframe' => 'Esta semana'
            ];
        }

        return $suggestions;
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\HasAuditFields;
use App\Events\VoterCaptureStatusChanged;
use App\Events\VoterCaptureConverted;
use Carbon\Carbon;

class VoterCapture extends Model
{
    use HasFactory, SoftDeletes, HasAuditFields;

    protected $fillable = [
        'first_name',
        'last_name',
        'document_number',
        'birth_date',
        'gender',
        'phone',
        'secondary_phone',
        'email',
        'address',
        'state_id',
        'municipality_id',
        'parish_id',
        'voting_center_id',
        'voting_table',
        'responsible_id',
        'capture_status',
        'capture_date',
        'last_contact_date',
        'next_contact_date',
        'contact_notes',
        'capture_source',
        'contact_method',
        'contact_attempts',
        'interest_level',
        'commitment_level',
        'willing_to_vote',
        'willing_to_mobilize',
        'needs_follow_up',
        'follow_up_date',
        'reminder_sent',
        'last_reminder_sent',
        'person_id',
        'converted_to_person',
        'conversion_date',
        'status',
        'notes',
        'additional_data',
        'created_by',
        'updated_by',
        'priority',
        'source_details',
        'latitude',
        'longitude',
        'campaign_id',
        'referral_code',
        'quality_score',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'capture_date' => 'date',
        'last_contact_date' => 'date',
        'next_contact_date' => 'date',
        'follow_up_date' => 'date',
        'willing_to_vote' => 'boolean',
        'willing_to_mobilize' => 'boolean',
        'needs_follow_up' => 'boolean',
        'reminder_sent' => 'boolean',
        'last_reminder_sent' => 'datetime',
        'converted_to_person' => 'boolean',
        'conversion_date' => 'datetime',
        'additional_data' => 'array',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'quality_score' => 'integer',
    ];

    /**
     * Relationship with geographic state
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Relationship with municipality
     */
    public function municipality(): BelongsTo
    {
        return $this->belongsTo(Municipality::class, 'municipality_id');
    }

    /**
     * Relationship with parish
     */
    public function parish(): BelongsTo
    {
        return $this->belongsTo(Parish::class, 'parish_id');
    }

    /**
     * Relationship with voting center
     */
    public function votingCenter(): BelongsTo
    {
        return $this->belongsTo(VotingCenter::class, 'voting_center_id');
    }

    /**
     * Relationship with responsible person (1x10 leader)
     */
    public function responsible(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'responsible_id');
    }

    /**
     * Relationship with converted person
     */
    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'person_id');
    }



    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get age attribute
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    /**
     * Get days since capture attribute
     */
    public function getDaysSinceCaptureAttribute(): int
    {
        return $this->capture_date->diffInDays(now());
    }

    /**
     * Get days until next contact attribute
     */
    public function getDaysUntilNextContactAttribute(): ?int
    {
        return $this->next_contact_date ? now()->diffInDays($this->next_contact_date, false) : null;
    }

    /**
     * Check if capture needs immediate attention
     */
    public function needsImmediateAttention(): bool
    {
        return $this->next_contact_date && $this->next_contact_date->isPast() && $this->capture_status === 'pending';
    }

    /**
     * Check if capture is overdue for contact
     */
    public function isOverdue(): bool
    {
        return $this->next_contact_date && $this->next_contact_date->isPast();
    }

    /**
     * Scope for pending captures
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('capture_status', 'pending');
    }

    /**
     * Scope for confirmed captures
     */
    public function scopeConfirmed(Builder $query): Builder
    {
        return $query->where('capture_status', 'confirmed');
    }

    /**
     * Scope for captures needing follow-up
     */
    public function scopeNeedsFollowUp(Builder $query): Builder
    {
        return $query->where('needs_follow_up', true);
    }

    /**
     * Scope for overdue captures
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('next_contact_date', '<', now())
                    ->where('capture_status', '!=', 'confirmed');
    }

    /**
     * Scope for captures by responsible person
     */
    public function scopeByResponsible(Builder $query, int $responsibleId): Builder
    {
        return $query->where('responsible_id', $responsibleId);
    }

    /**
     * Scope for text search
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($query) use ($search) {
            $query->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('document_number', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
        });
    }

    /**
     * Mark as contacted
     */
    public function markAsContacted(string $notes = null, string $nextContactDate = null): void
    {
        $oldStatus = $this->capture_status;

        $this->update([
            'capture_status' => 'contacted',
            'last_contact_date' => now(),
            'next_contact_date' => $nextContactDate ? Carbon::parse($nextContactDate) : null,
            'contact_attempts' => $this->contact_attempts + 1,
            'contact_notes' => $notes ? ($this->contact_notes ? $this->contact_notes . "\n\n" . $notes : $notes) : $this->contact_notes,
        ]);

        if ($oldStatus !== 'contacted') {
            VoterCaptureStatusChanged::dispatch($this, $oldStatus, 'contacted', $notes);
        }
    }

    /**
     * Mark as confirmed
     */
    public function markAsConfirmed(string $notes = null): void
    {
        $oldStatus = $this->capture_status;

        $this->update([
            'capture_status' => 'confirmed',
            'last_contact_date' => now(),
            'needs_follow_up' => false,
            'willing_to_vote' => true,
            'contact_notes' => $notes ? ($this->contact_notes ? $this->contact_notes . "\n\n" . $notes : $notes) : $this->contact_notes,
        ]);

        if ($oldStatus !== 'confirmed') {
            VoterCaptureStatusChanged::dispatch($this, $oldStatus, 'confirmed', $notes);
        }
    }

    /**
     * Convert to person
     */
    public function convertToPerson(): Person
    {
        $person = Person::create([
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'document_number' => $this->document_number,
            'birth_date' => $this->birth_date,
            'gender' => $this->gender,
            'phone' => $this->phone,
            'secondary_phone' => $this->secondary_phone,
            'email' => $this->email,
            'address' => $this->address,
            'state_id' => $this->state_id,
            'municipality_id' => $this->municipality_id,
            'parish_id' => $this->parish_id,
            'voting_center_id' => $this->voting_center_id,
            'voting_table' => $this->voting_table,
            'person_type' => 'voter',
            'assigned_leader_id' => $this->responsible_id,
            'status' => 'active',
            'notes' => $this->notes,
        ]);

        $this->update([
            'person_id' => $person->id,
            'converted_to_person' => true,
            'conversion_date' => now(),
            'status' => 'archived',
        ]);

        VoterCaptureConverted::dispatch($this, $person);

        return $person;
    }
}

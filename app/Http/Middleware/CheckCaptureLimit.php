<?php

namespace App\Http\Middleware;

use App\Models\VoterCapture;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckCaptureLimit
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only check for capture creation
        if (!$request->routeIs('admin.voter-captures.create') && !$request->isMethod('POST')) {
            return $next($request);
        }

        $user = auth()->user();
        
        // Skip check for super admins and coordinators
        if ($user->hasRole(['Super Admin', 'Coordinador de Personas'])) {
            return $next($request);
        }

        // Get user's person record
        $person = $user->person;
        if (!$person || !$person->is_leader_1x10) {
            return $next($request);
        }

        // Check daily limit
        $dailyLimit = config('voter_captures.limits.max_daily_captures', 20);
        $todayCaptures = VoterCapture::where('responsible_id', $person->id)
            ->whereDate('created_at', today())
            ->count();

        if ($todayCaptures >= $dailyLimit) {
            return redirect()
                ->route('admin.voter-captures.index')
                ->with('error', __('voter_captures.daily_limit_exceeded', ['limit' => $dailyLimit]));
        }

        // Check total limit per responsible
        $totalLimit = config('voter_captures.limits.max_captures_per_responsible', 50);
        $totalCaptures = VoterCapture::where('responsible_id', $person->id)
            ->where('status', 'active')
            ->count();

        if ($totalCaptures >= $totalLimit) {
            return redirect()
                ->route('admin.voter-captures.index')
                ->with('error', __('voter_captures.total_limit_exceeded', ['limit' => $totalLimit]));
        }

        return $next($request);
    }
}

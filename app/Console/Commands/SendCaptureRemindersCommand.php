<?php

namespace App\Console\Commands;

use App\Jobs\SendCaptureReminderJob;
use App\Models\VoterCapture;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendCaptureRemindersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'captures:send-reminders 
                           {--dry-run : Show what would be done without actually sending reminders}
                           {--force : Send reminders even if they were sent recently}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send reminder notifications for voter captures that need follow-up';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Starting capture reminders process...');

        // Get captures that need reminders
        $captures = $this->getCapturesNeedingReminders($force);

        if ($captures->isEmpty()) {
            $this->info('No captures need reminders at this time.');
            return self::SUCCESS;
        }

        $this->info("Found {$captures->count()} captures that need reminders.");

        if ($dryRun) {
            $this->showDryRunResults($captures);
            return self::SUCCESS;
        }

        // Send reminders
        $sent = 0;
        $failed = 0;

        foreach ($captures as $capture) {
            try {
                SendCaptureReminderJob::dispatch($capture);
                $sent++;
                
                $this->line("✓ Queued reminder for: {$capture->full_name} (ID: {$capture->id})");
                
            } catch (\Exception $e) {
                $failed++;
                $this->error("✗ Failed to queue reminder for: {$capture->full_name} (ID: {$capture->id})");
                Log::error("Failed to queue reminder for capture {$capture->id}: " . $e->getMessage());
            }
        }

        $this->newLine();
        $this->info("Reminders process completed:");
        $this->info("- Queued: {$sent}");
        
        if ($failed > 0) {
            $this->error("- Failed: {$failed}");
        }

        return self::SUCCESS;
    }

    /**
     * Get captures that need reminders
     */
    private function getCapturesNeedingReminders(bool $force = false): \Illuminate\Database\Eloquent\Collection
    {
        $query = VoterCapture::query()
            ->with(['responsible.user'])
            ->where('status', 'active')
            ->where('converted_to_person', false)
            ->where('needs_follow_up', true)
            ->whereIn('capture_status', ['pending', 'contacted'])
            ->whereHas('responsible.user'); // Only captures with responsible users

        // Check for overdue contacts
        $query->where(function ($q) {
            $q->where('next_contact_date', '<', now())
              ->orWhere(function ($subQ) {
                  $subQ->whereNull('next_contact_date')
                       ->where('capture_date', '<', now()->subDays(3)); // 3 days without contact date
              });
        });

        // Skip recently reminded unless forced
        if (!$force) {
            $query->where(function ($q) {
                $q->where('reminder_sent', false)
                  ->orWhere('last_reminder_sent', '<', now()->subHours(24));
            });
        }

        return $query->orderBy('next_contact_date')
                    ->orderBy('capture_date')
                    ->get();
    }

    /**
     * Show dry run results
     */
    private function showDryRunResults(\Illuminate\Database\Eloquent\Collection $captures): void
    {
        $this->info('DRY RUN - The following captures would receive reminders:');
        $this->newLine();

        $headers = ['ID', 'Voter Name', 'Document', 'Responsible', 'Status', 'Next Contact', 'Days Overdue'];
        $rows = [];

        foreach ($captures as $capture) {
            $daysOverdue = $capture->next_contact_date ? 
                now()->diffInDays($capture->next_contact_date) : 
                now()->diffInDays($capture->capture_date);

            $rows[] = [
                $capture->id,
                $capture->full_name,
                $capture->document_number,
                $capture->responsible->full_name,
                $capture->capture_status,
                $capture->next_contact_date ? $capture->next_contact_date->format('d/m/Y') : 'Not set',
                $daysOverdue,
            ];
        }

        $this->table($headers, $rows);
        
        $this->newLine();
        $this->info('Use --dry-run=false to actually send the reminders.');
    }
}

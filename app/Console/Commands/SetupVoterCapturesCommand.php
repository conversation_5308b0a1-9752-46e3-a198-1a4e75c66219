<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;

class SetupVoterCapturesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'voter-captures:setup 
                           {--force : Force setup even if already installed}
                           {--skip-migration : Skip running migrations}
                           {--skip-permissions : Skip creating permissions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup the voter captures module (migrations, permissions, etc.)';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚀 Setting up Voter Captures Module...');
        $this->newLine();

        // Check if already installed
        if (!$this->option('force') && $this->isAlreadyInstalled()) {
            $this->warn('⚠️  Voter captures module appears to be already installed.');
            
            if (!$this->confirm('Do you want to continue anyway?')) {
                $this->info('Setup cancelled.');
                return self::SUCCESS;
            }
        }

        // Run migrations
        if (!$this->option('skip-migration')) {
            $this->runMigrations();
        }

        // Setup permissions
        if (!$this->option('skip-permissions')) {
            $this->setupPermissions();
        }

        // Verify installation
        $this->verifyInstallation();

        $this->newLine();
        $this->info('✅ Voter Captures Module setup completed successfully!');
        $this->newLine();

        // Show next steps
        $this->showNextSteps();

        return self::SUCCESS;
    }

    /**
     * Check if the module is already installed
     */
    private function isAlreadyInstalled(): bool
    {
        return Schema::hasTable('voter_captures');
    }

    /**
     * Run database migrations
     */
    private function runMigrations(): void
    {
        $this->info('📊 Running database migrations...');

        try {
            Artisan::call('migrate', [
                '--path' => 'database/migrations/2024_12_01_000010_create_voter_captures_table.php',
                '--force' => true,
            ]);

            $this->info('✅ Migrations completed successfully.');
        } catch (\Exception $e) {
            $this->error('❌ Migration failed: ' . $e->getMessage());
            return;
        }
    }

    /**
     * Setup permissions and roles
     */
    private function setupPermissions(): void
    {
        $this->info('🔐 Setting up permissions...');

        try {
            Artisan::call('db:seed', [
                '--class' => 'VoterCapturePermissionSeeder',
                '--force' => true,
            ]);

            $this->info('✅ Permissions setup completed.');
        } catch (\Exception $e) {
            $this->error('❌ Permissions setup failed: ' . $e->getMessage());
            $this->warn('You may need to run the seeder manually:');
            $this->line('php artisan db:seed --class=VoterCapturePermissionSeeder');
        }
    }

    /**
     * Verify the installation
     */
    private function verifyInstallation(): void
    {
        $this->info('🔍 Verifying installation...');

        $checks = [
            'Database table exists' => Schema::hasTable('voter_captures'),
            'VoterCapture model exists' => class_exists(\App\Models\VoterCapture::class),
            'Livewire components exist' => class_exists(\App\Livewire\Admin\VoterCaptures\Pages\VoterCaptures::class),
            'Translation files exist' => file_exists(lang_path('es/voter_captures.php')),
            'Configuration file exists' => file_exists(config_path('voter_captures.php')),
        ];

        foreach ($checks as $check => $result) {
            if ($result) {
                $this->info("✅ {$check}");
            } else {
                $this->error("❌ {$check}");
            }
        }
    }

    /**
     * Show next steps to the user
     */
    private function showNextSteps(): void
    {
        $this->info('📋 Next Steps:');
        $this->newLine();

        $steps = [
            '1. Assign permissions to users:',
            '   php artisan tinker',
            '   $user = User::find(1);',
            '   $user->assignRole(\'Coordinador de Personas\');',
            '',
            '2. Configure queue worker for notifications:',
            '   php artisan queue:work',
            '',
            '3. Setup cron job for automatic reminders:',
            '   * * * * * cd /path-to-project && php artisan schedule:run >> /dev/null 2>&1',
            '',
            '4. Access the module at:',
            '   /admin/voter-captures',
            '',
            '5. Test reminder system:',
            '   php artisan captures:send-reminders --dry-run',
        ];

        foreach ($steps as $step) {
            if (empty($step)) {
                $this->newLine();
            } else {
                $this->line($step);
            }
        }

        $this->newLine();
        $this->info('📖 For detailed documentation, see: docs/MODULO_CAPTACION_VOTANTES.md');
    }
}

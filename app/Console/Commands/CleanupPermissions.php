<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class CleanupPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:cleanup {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old Spanish permissions and duplicates';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧹 Iniciando limpieza de permisos...');

        $spanishPermissions = [
            'view personas',
            'create personas',
            'update personas',
            'delete personas',
            'export personas',
            'import personas',
            'assign lider personas',
            'create user from persona',
            'view own personas',
            'view ubicaciones',
            'create ubicaciones',
            'update ubicaciones',
            'delete ubicaciones',
            'export ubicaciones',
            'import ubicaciones',
            'view eventos_electorales',
            'create eventos_electorales',
            'update eventos_electorales',
            'delete eventos_electorales',
            'export eventos_electorales',
            'view movilizaciones',
            'create movilizaciones',
            'update movilizaciones',
            'delete movilizaciones',
            'export movilizaciones',
        ];

        $deletedCount = 0;
        $foundPermissions = [];

        foreach ($spanishPermissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            
            if ($permission) {
                $foundPermissions[] = $permissionName;
                
                if (!$this->option('dry-run')) {
                    // Remove permission from all roles first
                    $roles = Role::whereHas('permissions', function ($query) use ($permissionName) {
                        $query->where('name', $permissionName);
                    })->get();

                    foreach ($roles as $role) {
                        $role->revokePermissionTo($permissionName);
                        $this->line("   🔄 Removido permiso '{$permissionName}' del rol '{$role->name}'");
                    }

                    // Delete the permission
                    $permission->delete();
                    $deletedCount++;
                    $this->line("   ❌ Eliminado permiso: '{$permissionName}'");
                } else {
                    $this->line("   🔍 Encontrado permiso en español: '{$permissionName}'");
                }
            }
        }

        if ($this->option('dry-run')) {
            $this->info("\n📊 MODO DRY-RUN - No se eliminó nada");
            $this->info("Permisos en español encontrados: " . count($foundPermissions));
            
            if (count($foundPermissions) > 0) {
                $this->warn("Para eliminar estos permisos, ejecuta: php artisan permissions:cleanup");
            } else {
                $this->info("✅ No se encontraron permisos en español para limpiar");
            }
        } else {
            $this->info("\n✅ Limpieza completada");
            $this->info("Permisos eliminados: {$deletedCount}");
            
            if ($deletedCount > 0) {
                $this->warn("⚠️  Recuerda ejecutar los seeders para asegurar que todos los permisos estén actualizados:");
                $this->line("php artisan db:seed --class=PermissionSeeder");
                $this->line("php artisan db:seed --class=RoleSeeder");
            }
        }

        // Show current permission count
        $totalPermissions = Permission::count();
        $this->info("\n📊 Total de permisos en el sistema: {$totalPermissions}");

        // Show permissions with Spanish-like names
        $suspiciousPermissions = Permission::where('name', 'like', '%personas%')
            ->orWhere('name', 'like', '%ubicaciones%')
            ->orWhere('name', 'like', '%eventos_electorales%')
            ->orWhere('name', 'like', '%movilizaciones%')
            ->get();

        if ($suspiciousPermissions->count() > 0) {
            $this->warn("\n⚠️  Permisos sospechosos encontrados:");
            foreach ($suspiciousPermissions as $permission) {
                $this->line("   - {$permission->name}");
            }
        } else {
            $this->info("\n✅ No se encontraron permisos con nombres en español");
        }

        return 0;
    }
}

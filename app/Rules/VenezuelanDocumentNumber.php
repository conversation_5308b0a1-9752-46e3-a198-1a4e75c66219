<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class VenezuelanDocumentNumber implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Remove any non-numeric characters
        $cleanValue = preg_replace('/[^0-9]/', '', $value);
        
        // Check if it's empty after cleaning
        if (empty($cleanValue)) {
            $fail('El número de cédula es requerido.');
            return;
        }
        
        // Check minimum length (Venezuelan IDs are typically 7-8 digits)
        if (strlen($cleanValue) < 6) {
            $fail('El número de cédula debe tener al menos 6 dígitos.');
            return;
        }
        
        // Check maximum length
        if (strlen($cleanValue) > 10) {
            $fail('El número de cédula no puede tener más de 10 dígitos.');
            return;
        }
        
        // Check for obviously invalid patterns
        if ($this->isInvalidPattern($cleanValue)) {
            $fail('El número de cédula no tiene un formato válido.');
            return;
        }
    }
    
    /**
     * Check for invalid patterns
     */
    private function isInvalidPattern(string $value): bool
    {
        // All same digits (e.g., 1111111)
        if (preg_match('/^(\d)\1+$/', $value)) {
            return true;
        }
        
        // Sequential numbers (e.g., 1234567)
        if ($this->isSequential($value)) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if the number is sequential
     */
    private function isSequential(string $value): bool
    {
        $digits = str_split($value);
        $ascending = true;
        $descending = true;
        
        for ($i = 1; $i < count($digits); $i++) {
            if ($digits[$i] != $digits[$i-1] + 1) {
                $ascending = false;
            }
            if ($digits[$i] != $digits[$i-1] - 1) {
                $descending = false;
            }
        }
        
        return $ascending || $descending;
    }
}

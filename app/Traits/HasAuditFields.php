<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HasAuditFields
{
    /**
     * Boot the trait
     */
    protected static function bootHasAuditFields(): void
    {
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = auth()->id();
                $model->updated_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->id();
            }
        });
    }

    /**
     * Relationship with user who created this record
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship with user who last updated this record
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the name of the user who created this record
     */
    public function getCreatedByNameAttribute(): string
    {
        return $this->createdBy?->name ?? 'Sistema';
    }

    /**
     * Get the name of the user who last updated this record
     */
    public function getUpdatedByNameAttribute(): string
    {
        return $this->updatedBy?->name ?? 'Sistema';
    }
}

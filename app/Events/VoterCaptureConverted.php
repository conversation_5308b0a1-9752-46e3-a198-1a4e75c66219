<?php

namespace App\Events;

use App\Models\VoterCapture;
use App\Models\Person;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VoterCaptureConverted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public VoterCapture $capture,
        public Person $person
    ) {}
}

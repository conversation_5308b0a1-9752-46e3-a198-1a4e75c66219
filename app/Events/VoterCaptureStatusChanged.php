<?php

namespace App\Events;

use App\Models\VoterCapture;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class VoterCaptureStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public VoterCapture $capture,
        public string $oldStatus,
        public string $newStatus,
        public ?string $notes = null
    ) {}
}

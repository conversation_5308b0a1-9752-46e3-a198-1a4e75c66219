<?php

namespace App\Listeners;

use App\Events\VoterCaptureStatusChanged;
use Illuminate\Support\Facades\Log;

class LogVoterCaptureStatusChange
{
    /**
     * Handle the event.
     */
    public function handle(VoterCaptureStatusChanged $event): void
    {
        Log::info('Voter capture status changed', [
            'capture_id' => $event->capture->id,
            'voter_name' => $event->capture->full_name,
            'document_number' => $event->capture->document_number,
            'old_status' => $event->oldStatus,
            'new_status' => $event->newStatus,
            'responsible_id' => $event->capture->responsible_id,
            'responsible_name' => $event->capture->responsible->full_name ?? 'N/A',
            'changed_by' => auth()->user()?->name ?? 'System',
            'notes' => $event->notes,
            'timestamp' => now()->toISOString(),
        ]);
    }
}

<?php

namespace App\Listeners;

use App\Events\VoterCaptureStatusChanged;
use App\Notifications\CaptureStatusChangedNotification;

class NotifyResponsibleOfStatusChange
{
    /**
     * Handle the event.
     */
    public function handle(VoterCaptureStatusChanged $event): void
    {
        // Only notify for important status changes
        $importantStatuses = ['confirmed', 'not_interested', 'unreachable'];
        
        if (!in_array($event->newStatus, $importantStatuses)) {
            return;
        }
        
        $responsible = $event->capture->responsible;
        
        if (!$responsible || !$responsible->user) {
            return;
        }
        
        try {
            $responsible->user->notify(
                new CaptureStatusChangedNotification($event->capture, $event->oldStatus, $event->newStatus)
            );
        } catch (\Exception $e) {
            \Log::error('Failed to send status change notification', [
                'capture_id' => $event->capture->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}

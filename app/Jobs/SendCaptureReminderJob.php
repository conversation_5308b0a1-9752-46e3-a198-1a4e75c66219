<?php

namespace App\Jobs;

use App\Models\VoterCapture;
use App\Notifications\CaptureReminderNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendCaptureReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public VoterCapture $capture
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Check if the capture still needs a reminder
            if (!$this->shouldSendReminder()) {
                Log::info("Reminder not needed for capture {$this->capture->id}");
                return;
            }

            // Get the responsible person
            $responsible = $this->capture->responsible;
            
            if (!$responsible || !$responsible->user) {
                Log::warning("No responsible user found for capture {$this->capture->id}");
                return;
            }

            // Send notification to responsible person
            $responsible->user->notify(new CaptureReminderNotification($this->capture));

            // Update capture reminder status
            $this->capture->update([
                'reminder_sent' => true,
                'last_reminder_sent' => now(),
            ]);

            Log::info("Reminder sent successfully for capture {$this->capture->id}");

        } catch (\Exception $e) {
            Log::error("Failed to send reminder for capture {$this->capture->id}: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if reminder should be sent
     */
    private function shouldSendReminder(): bool
    {
        // Refresh the model to get latest data
        $this->capture->refresh();

        // Don't send if already converted to person
        if ($this->capture->converted_to_person) {
            return false;
        }

        // Don't send if status is confirmed or not interested
        if (in_array($this->capture->capture_status, ['confirmed', 'not_interested', 'unreachable'])) {
            return false;
        }

        // Don't send if no follow-up needed
        if (!$this->capture->needs_follow_up) {
            return false;
        }

        // Don't send if next contact date is in the future
        if ($this->capture->next_contact_date && $this->capture->next_contact_date->isFuture()) {
            return false;
        }

        // Don't send if reminder was sent recently (within 24 hours)
        if ($this->capture->last_reminder_sent && $this->capture->last_reminder_sent->diffInHours(now()) < 24) {
            return false;
        }

        return true;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("SendCaptureReminderJob failed for capture {$this->capture->id}: " . $exception->getMessage());
    }
}

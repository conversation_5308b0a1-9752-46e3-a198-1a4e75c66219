<?php

namespace App\Notifications;

use App\Models\VoterCapture;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CaptureReminderNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public VoterCapture $capture
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $url = route('admin.voter-captures.show', $this->capture);

        return (new MailMessage)
            ->subject(__('voter_captures.reminder_subject'))
            ->greeting(__('voter_captures.reminder_greeting', ['name' => $notifiable->name]))
            ->line(__('voter_captures.reminder_message', [
                'voter_name' => $this->capture->full_name,
                'document' => $this->capture->document_number,
            ]))
            ->line($this->getContactDetails())
            ->line($this->getStatusDetails())
            ->action(__('voter_captures.view_capture'), $url)
            ->line(__('voter_captures.reminder_footer'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'capture_reminder',
            'capture_id' => $this->capture->id,
            'voter_name' => $this->capture->full_name,
            'document_number' => $this->capture->document_number,
            'capture_status' => $this->capture->capture_status,
            'next_contact_date' => $this->capture->next_contact_date?->format('Y-m-d'),
            'days_overdue' => $this->capture->next_contact_date ? 
                now()->diffInDays($this->capture->next_contact_date) : null,
            'message' => __('voter_captures.reminder_database_message', [
                'voter_name' => $this->capture->full_name,
            ]),
        ];
    }

    /**
     * Get contact details for the email
     */
    private function getContactDetails(): string
    {
        $details = [];
        
        if ($this->capture->phone) {
            $details[] = __('voter_captures.phone') . ': ' . $this->capture->phone;
        }
        
        if ($this->capture->email) {
            $details[] = __('voter_captures.email') . ': ' . $this->capture->email;
        }
        
        if ($this->capture->address) {
            $details[] = __('voter_captures.address') . ': ' . $this->capture->address;
        }

        return empty($details) ? 
            __('voter_captures.no_contact_details') : 
            implode(' | ', $details);
    }

    /**
     * Get status details for the email
     */
    private function getStatusDetails(): string
    {
        $status = __('voter_captures.' . $this->capture->capture_status);
        $captureDate = $this->capture->capture_date->format('d/m/Y');
        
        $details = __('voter_captures.status') . ': ' . $status . ' | ' .
                  __('voter_captures.capture_date') . ': ' . $captureDate;

        if ($this->capture->next_contact_date) {
            $nextContact = $this->capture->next_contact_date->format('d/m/Y');
            $daysOverdue = now()->diffInDays($this->capture->next_contact_date);
            
            $details .= ' | ' . __('voter_captures.next_contact_date') . ': ' . $nextContact;
            
            if ($this->capture->next_contact_date->isPast()) {
                $details .= ' (' . __('voter_captures.days_overdue', ['days' => $daysOverdue]) . ')';
            }
        }

        if ($this->capture->contact_attempts > 0) {
            $details .= ' | ' . __('voter_captures.contact_attempts') . ': ' . $this->capture->contact_attempts;
        }

        return $details;
    }
}

<?php

namespace App\Notifications;

use App\Models\VoterCapture;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CaptureStatusChangedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public VoterCapture $capture,
        public string $oldStatus,
        public string $newStatus
    ) {}

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $url = route('admin.voter-captures.show', $this->capture);
        
        $statusColor = match($this->newStatus) {
            'confirmed' => 'success',
            'not_interested' => 'error',
            'unreachable' => 'warning',
            default => 'info'
        };

        return (new MailMessage)
            ->subject(__('voter_captures.status_change_subject'))
            ->greeting(__('voter_captures.status_change_greeting', ['name' => $notifiable->name]))
            ->line(__('voter_captures.status_change_message', [
                'voter_name' => $this->capture->full_name,
                'document' => $this->capture->document_number,
                'old_status' => __('voter_captures.' . $this->oldStatus),
                'new_status' => __('voter_captures.' . $this->newStatus),
            ]))
            ->when($this->capture->contact_notes, function ($mail) {
                return $mail->line(__('voter_captures.latest_notes') . ': ' . $this->capture->contact_notes);
            })
            ->action(__('voter_captures.view_capture'), $url)
            ->line(__('voter_captures.status_change_footer'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'capture_status_changed',
            'capture_id' => $this->capture->id,
            'voter_name' => $this->capture->full_name,
            'document_number' => $this->capture->document_number,
            'old_status' => $this->oldStatus,
            'new_status' => $this->newStatus,
            'message' => __('voter_captures.status_change_database_message', [
                'voter_name' => $this->capture->full_name,
                'new_status' => __('voter_captures.' . $this->newStatus),
            ]),
        ];
    }
}

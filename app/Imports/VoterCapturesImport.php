<?php

namespace App\Imports;

use App\Models\VoterCapture;
use App\Models\Person;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use App\Models\VotingCenter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class VoterCapturesImport implements ToCollection, WithHeadingRow
{
    private int $totalRows = 0;
    private int $successfulImports = 0;
    private int $failedImports = 0;
    private int $updatedRecords = 0;
    private array $errors = [];

    public function __construct(
        private bool $hasHeaders = true,
        private bool $updateExisting = false,
        private ?string $defaultResponsibleId = null,
        private ?string $defaultCampaignId = null,
        private string $defaultPriority = 'medium'
    ) {}

    public function collection(Collection $rows): void
    {
        $this->totalRows = $rows->count();

        foreach ($rows as $index => $row) {
            try {
                $this->processRow($row->toArray(), $index + 1);
            } catch (\Exception $e) {
                $this->failedImports++;
                $this->errors[] = [
                    'row' => $index + 1,
                    'error' => $e->getMessage(),
                    'data' => $row->toArray()
                ];
            }
        }
    }

    private function processRow(array $row, int $rowNumber): void
    {
        // Validate required fields
        $validator = Validator::make($row, [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'document_number' => 'required|string|max:20',
        ]);

        if ($validator->fails()) {
            throw new \Exception('Validation failed: ' . implode(', ', $validator->errors()->all()));
        }

        // Clean document number
        $documentNumber = preg_replace('/[^0-9]/', '', $row['document_number']);

        // Check if capture already exists
        $existingCapture = VoterCapture::where('document_number', $documentNumber)->first();

        if ($existingCapture && !$this->updateExisting) {
            throw new \Exception('Capture already exists and update is disabled');
        }

        // Find or resolve related entities
        $stateId = $this->findStateId($row['state_name'] ?? null);
        $municipalityId = $this->findMunicipalityId($row['municipality_name'] ?? null, $stateId);
        $parishId = $this->findParishId($row['parish_name'] ?? null, $municipalityId);
        $votingCenterId = $this->findVotingCenterId($row['voting_center_name'] ?? null, $parishId);
        $responsibleId = $this->findResponsibleId($row['responsible_document'] ?? null) ?? $this->defaultResponsibleId;

        if (!$responsibleId) {
            throw new \Exception('No responsible person found or specified');
        }

        // Prepare data
        $data = [
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
            'document_number' => $documentNumber,
            'birth_date' => $this->parseDate($row['birth_date'] ?? null),
            'gender' => $this->parseGender($row['gender'] ?? null),
            'phone' => $row['phone'] ?? null,
            'secondary_phone' => $row['secondary_phone'] ?? null,
            'email' => $row['email'] ?? null,
            'address' => $row['address'] ?? null,
            'state_id' => $stateId,
            'municipality_id' => $municipalityId,
            'parish_id' => $parishId,
            'voting_center_id' => $votingCenterId,
            'voting_table' => $row['voting_table'] ?? null,
            'responsible_id' => $responsibleId,
            'capture_status' => 'pending',
            'capture_date' => now(),
            'capture_source' => $this->parseEnum($row['capture_source'] ?? 'other', [
                'door_to_door', 'phone_call', 'social_media', 'event', 'referral', 'other'
            ], 'other'),
            'source_details' => $row['source_details'] ?? null,
            'contact_method' => $this->parseEnum($row['contact_method'] ?? 'phone', [
                'phone', 'whatsapp', 'visit', 'email', 'social_media'
            ], 'phone'),
            'interest_level' => $this->parseEnum($row['interest_level'] ?? null, [
                'high', 'medium', 'low', 'none'
            ]),
            'commitment_level' => $this->parseEnum($row['commitment_level'] ?? null, [
                'committed', 'likely', 'uncertain', 'unlikely'
            ]),
            'willing_to_vote' => $this->parseBoolean($row['willing_to_vote'] ?? false),
            'willing_to_mobilize' => $this->parseBoolean($row['willing_to_mobilize'] ?? false),
            'needs_follow_up' => true,
            'priority' => $this->parseEnum($row['priority'] ?? $this->defaultPriority, [
                'low', 'medium', 'high', 'urgent'
            ], $this->defaultPriority),
            'campaign_id' => $row['campaign_id'] ?? $this->defaultCampaignId,
            'referral_code' => $row['referral_code'] ?? null,
            'status' => 'active',
            'notes' => $row['notes'] ?? null,
            'created_by' => auth()->id(),
            'updated_by' => auth()->id(),
        ];

        if ($existingCapture && $this->updateExisting) {
            $existingCapture->update($data);
            $this->updatedRecords++;
        } else {
            VoterCapture::create($data);
            $this->successfulImports++;
        }
    }

    private function findStateId(?string $stateName): ?int
    {
        if (!$stateName) return null;
        return State::where('name', 'like', "%{$stateName}%")->first()?->id;
    }

    private function findMunicipalityId(?string $municipalityName, ?int $stateId): ?int
    {
        if (!$municipalityName) return null;
        
        $query = Municipality::where('name', 'like', "%{$municipalityName}%");
        if ($stateId) {
            $query->where('state_id', $stateId);
        }
        
        return $query->first()?->id;
    }

    private function findParishId(?string $parishName, ?int $municipalityId): ?int
    {
        if (!$parishName) return null;
        
        $query = Parish::where('name', 'like', "%{$parishName}%");
        if ($municipalityId) {
            $query->where('municipality_id', $municipalityId);
        }
        
        return $query->first()?->id;
    }

    private function findVotingCenterId(?string $centerName, ?int $parishId): ?int
    {
        if (!$centerName) return null;
        
        $query = VotingCenter::where('name', 'like', "%{$centerName}%");
        if ($parishId) {
            $query->where('parish_id', $parishId);
        }
        
        return $query->first()?->id;
    }

    private function findResponsibleId(?string $documentNumber): ?int
    {
        if (!$documentNumber) return null;
        
        $cleanDocument = preg_replace('/[^0-9]/', '', $documentNumber);
        return Person::where('document_number', $cleanDocument)
                    ->where('is_leader_1x10', true)
                    ->first()?->id;
    }

    private function parseDate(?string $date): ?string
    {
        if (!$date) return null;
        
        try {
            return \Carbon\Carbon::parse($date)->format('Y-m-d');
        } catch (\Exception $e) {
            return null;
        }
    }

    private function parseGender(?string $gender): ?string
    {
        if (!$gender) return null;
        
        $gender = strtoupper(substr($gender, 0, 1));
        return in_array($gender, ['M', 'F', 'O']) ? $gender : null;
    }

    private function parseEnum(?string $value, array $validValues, ?string $default = null): ?string
    {
        if (!$value) return $default;
        
        $value = strtolower(trim($value));
        return in_array($value, $validValues) ? $value : $default;
    }

    private function parseBoolean($value): bool
    {
        if (is_bool($value)) return $value;
        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'si', 'sí']);
        }
        return (bool) $value;
    }

    // Getters for results
    public function getTotalRows(): int { return $this->totalRows; }
    public function getSuccessfulImports(): int { return $this->successfulImports; }
    public function getFailedImports(): int { return $this->failedImports; }
    public function getUpdatedRecords(): int { return $this->updatedRecords; }
    public function getErrors(): array { return $this->errors; }
}

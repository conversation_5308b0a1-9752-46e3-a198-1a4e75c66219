<?php

namespace App\Observers;

use App\Models\VoterCapture;
use App\Events\VoterCaptureStatusChanged;
use Illuminate\Support\Facades\Log;

class VoterCaptureObserver
{
    /**
     * Handle the VoterCapture "creating" event.
     */
    public function creating(VoterCapture $voterCapture): void
    {
        // Auto-calculate quality score based on available data
        $voterCapture->quality_score = $this->calculateQualityScore($voterCapture);
        
        // Set default next contact date if not provided
        if (!$voterCapture->next_contact_date && $voterCapture->needs_follow_up) {
            $defaultDays = config('voter_captures.contact.default_next_contact_days', 3);
            $voterCapture->next_contact_date = now()->addDays($defaultDays);
        }
    }

    /**
     * Handle the VoterCapture "created" event.
     */
    public function created(VoterCapture $voterCapture): void
    {
        Log::info('New voter capture created', [
            'capture_id' => $voterCapture->id,
            'voter_name' => $voterCapture->full_name,
            'document_number' => $voterCapture->document_number,
            'responsible_id' => $voterCapture->responsible_id,
            'created_by' => $voterCapture->created_by,
        ]);
    }

    /**
     * Handle the VoterCapture "updating" event.
     */
    public function updating(VoterCapture $voterCapture): void
    {
        // Check if status is changing
        if ($voterCapture->isDirty('capture_status')) {
            $oldStatus = $voterCapture->getOriginal('capture_status');
            $newStatus = $voterCapture->capture_status;
            
            // Auto-update fields based on status change
            $this->handleStatusChange($voterCapture, $oldStatus, $newStatus);
        }

        // Recalculate quality score if relevant fields changed
        if ($this->shouldRecalculateQuality($voterCapture)) {
            $voterCapture->quality_score = $this->calculateQualityScore($voterCapture);
        }
    }

    /**
     * Handle the VoterCapture "updated" event.
     */
    public function updated(VoterCapture $voterCapture): void
    {
        // Log status changes
        if ($voterCapture->wasChanged('capture_status')) {
            $oldStatus = $voterCapture->getOriginal('capture_status');
            $newStatus = $voterCapture->capture_status;
            
            Log::info('Voter capture status changed', [
                'capture_id' => $voterCapture->id,
                'voter_name' => $voterCapture->full_name,
                'old_status' => $oldStatus,
                'new_status' => $newStatus,
                'updated_by' => $voterCapture->updated_by,
            ]);
        }
    }

    /**
     * Handle the VoterCapture "deleted" event.
     */
    public function deleted(VoterCapture $voterCapture): void
    {
        Log::warning('Voter capture deleted', [
            'capture_id' => $voterCapture->id,
            'voter_name' => $voterCapture->full_name,
            'document_number' => $voterCapture->document_number,
            'deleted_by' => auth()->id(),
        ]);
    }

    /**
     * Calculate quality score based on available data
     */
    private function calculateQualityScore(VoterCapture $voterCapture): int
    {
        $score = 0;

        // Basic information completeness (40 points)
        if ($voterCapture->first_name) $score += 5;
        if ($voterCapture->last_name) $score += 5;
        if ($voterCapture->document_number) $score += 10;
        if ($voterCapture->phone) $score += 10;
        if ($voterCapture->email) $score += 5;
        if ($voterCapture->address) $score += 5;

        // Location information (20 points)
        if ($voterCapture->state_id) $score += 5;
        if ($voterCapture->municipality_id) $score += 5;
        if ($voterCapture->parish_id) $score += 5;
        if ($voterCapture->voting_center_id) $score += 5;

        // Engagement level (30 points)
        if ($voterCapture->interest_level) {
            $score += match($voterCapture->interest_level) {
                'high' => 10,
                'medium' => 7,
                'low' => 4,
                'none' => 0,
                default => 0
            };
        }

        if ($voterCapture->commitment_level) {
            $score += match($voterCapture->commitment_level) {
                'committed' => 10,
                'likely' => 7,
                'uncertain' => 4,
                'unlikely' => 1,
                default => 0
            };
        }

        if ($voterCapture->willing_to_vote) $score += 5;
        if ($voterCapture->willing_to_mobilize) $score += 5;

        // Contact history (10 points)
        if ($voterCapture->contact_attempts > 0) {
            $score += min($voterCapture->contact_attempts * 2, 10);
        }

        return min($score, 100); // Cap at 100
    }

    /**
     * Handle automatic field updates based on status change
     */
    private function handleStatusChange(VoterCapture $voterCapture, string $oldStatus, string $newStatus): void
    {
        switch ($newStatus) {
            case 'confirmed':
                $voterCapture->needs_follow_up = false;
                $voterCapture->willing_to_vote = true;
                $voterCapture->last_contact_date = now();
                break;
                
            case 'not_interested':
            case 'unreachable':
                $voterCapture->needs_follow_up = false;
                $voterCapture->next_contact_date = null;
                break;
                
            case 'contacted':
                $voterCapture->last_contact_date = now();
                if (!$voterCapture->next_contact_date) {
                    $defaultDays = config('voter_captures.contact.default_next_contact_days', 3);
                    $voterCapture->next_contact_date = now()->addDays($defaultDays);
                }
                break;
        }
    }

    /**
     * Check if quality score should be recalculated
     */
    private function shouldRecalculateQuality(VoterCapture $voterCapture): bool
    {
        $qualityFields = [
            'phone', 'email', 'address', 'state_id', 'municipality_id', 
            'parish_id', 'voting_center_id', 'interest_level', 
            'commitment_level', 'willing_to_vote', 'willing_to_mobilize',
            'contact_attempts'
        ];

        foreach ($qualityFields as $field) {
            if ($voterCapture->isDirty($field)) {
                return true;
            }
        }

        return false;
    }
}

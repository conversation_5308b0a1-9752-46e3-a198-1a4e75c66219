<?php

/**
 * <PERSON>ript de prueba rápida para verificar el mapeo de ubicaciones
 */

require_once 'vendor/autoload.php';

use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;

echo "🧪 PRUEBA RÁPIDA DE MAPEO DE UBICACIONES\n";
echo "=" . str_repeat("=", 50) . "\n\n";

// Función para crear mapa de estados (copiada del seeder)
function createStateMap(): array
{
    $states = State::all();
    $map = [];
    
    foreach ($states as $state) {
        $variations = [
            $state->name,
            strtoupper($state->name),
            ucfirst(strtolower($state->name)),
            str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($state->name)),
            str_replace(['Edo. ', 'Dtto. ', 'Estado '], '', $state->name),
            str_replace(['Á', 'É', 'Í', 'Ó', 'Ú'], ['A', 'E', 'I', 'O', 'U'], strtoupper($state->name)),
        ];
        
        if ($state->name === 'Distrito Capital') {
            $variations[] = 'CAPITAL';
            $variations[] = 'DTTO. CAPITAL';
            $variations[] = 'DISTRITO CAPITAL';
        }
        
        if ($state->name === 'La Guaira') {
            $variations[] = 'VARGAS';
            $variations[] = 'LA GUAIRA';
        }
        
        foreach ($variations as $variation) {
            $map[trim($variation)] = $state->id;
        }
    }
    
    return $map;
}

// Función para crear mapa de municipios
function createMunicipalityMap(): array
{
    $municipalities = Municipality::with('state')->get();
    $map = [];
    
    foreach ($municipalities as $municipality) {
        $stateVariations = [
            $municipality->state->name,
            strtoupper($municipality->state->name),
            str_replace(['EDO. ', 'DTTO. ', 'ESTADO '], '', strtoupper($municipality->state->name)),
        ];
        
        if ($municipality->state->name === 'Distrito Capital') {
            $stateVariations[] = 'CAPITAL';
        }
        
        $municipalityVariations = [
            $municipality->name,
            strtoupper($municipality->name),
            str_replace(['MP. ', 'MUNICIPIO ', 'MUN. '], '', strtoupper($municipality->name)),
        ];
        
        foreach ($stateVariations as $stateVar) {
            foreach ($municipalityVariations as $munVar) {
                $key = trim($stateVar) . '|' . trim($munVar);
                $map[$key] = $municipality->id;
            }
        }
    }
    
    return $map;
}

try {
    echo "1️⃣  Creando mapas de ubicaciones...\n";
    $stateMap = createStateMap();
    $municipalityMap = createMunicipalityMap();
    
    echo "   📊 Estados mapeados: " . count($stateMap) . "\n";
    echo "   📊 Municipios mapeados: " . count($municipalityMap) . "\n\n";
    
    echo "2️⃣  Probando casos específicos del CSV...\n";
    
    // Casos de prueba basados en el CSV
    $testCases = [
        ['state' => 'Amazonas', 'municipality' => 'Alto Orinoco', 'parish' => 'Huachamacare'],
        ['state' => 'Anzoátegui', 'municipality' => 'Anaco', 'parish' => 'Anaco'],
        ['state' => 'CAPITAL', 'municipality' => 'LIBERTADOR', 'parish' => 'CATEDRAL'],
        ['state' => 'VARGAS', 'municipality' => 'VARGAS', 'parish' => 'CATIA LA MAR'],
    ];
    
    foreach ($testCases as $i => $test) {
        echo "   🔍 Caso " . ($i + 1) . ": {$test['state']} | {$test['municipality']} | {$test['parish']}\n";
        
        // Probar estado
        $stateId = $stateMap[$test['state']] ?? null;
        if ($stateId) {
            $stateName = State::find($stateId)->name;
            echo "      ✅ Estado encontrado: {$test['state']} -> {$stateName} (ID: {$stateId})\n";
            
            // Probar municipio
            $municipalityKey = $test['state'] . '|' . $test['municipality'];
            $municipalityId = $municipalityMap[$municipalityKey] ?? null;
            
            if ($municipalityId) {
                $municipalityName = Municipality::find($municipalityId)->name;
                echo "      ✅ Municipio encontrado: {$test['municipality']} -> {$municipalityName} (ID: {$municipalityId})\n";
            } else {
                echo "      ❌ Municipio NO encontrado: {$municipalityKey}\n";
                
                // Mostrar municipios disponibles para este estado
                $availableMunicipalities = Municipality::where('state_id', $stateId)->pluck('name')->toArray();
                echo "         Municipios disponibles: " . implode(', ', array_slice($availableMunicipalities, 0, 5)) . "\n";
            }
        } else {
            echo "      ❌ Estado NO encontrado: {$test['state']}\n";
            
            // Mostrar estados disponibles
            $availableStates = array_slice(array_keys($stateMap), 0, 10);
            echo "         Estados disponibles: " . implode(', ', $availableStates) . "\n";
        }
        echo "\n";
    }
    
    echo "3️⃣  Verificando primeros registros del CSV...\n";
    
    $csvFile = 'database/seeders/data/voting_centers.csv';
    if (file_exists($csvFile)) {
        $handle = fopen($csvFile, 'r');
        $header = fgetcsv($handle);
        
        $count = 0;
        while (($row = fgetcsv($handle)) !== false && $count < 5) {
            $data = array_combine($header, $row);
            $count++;
            
            echo "   📄 Registro {$count}: {$data['code']} - {$data['name']}\n";
            echo "      Ubicación: {$data['state_name']} | {$data['municipality_name']} | {$data['parish_name']}\n";
            
            // Verificar mapeo
            $stateId = $stateMap[$data['state_name']] ?? null;
            $municipalityKey = $data['state_name'] . '|' . $data['municipality_name'];
            $municipalityId = $municipalityMap[$municipalityKey] ?? null;
            
            if ($stateId && $municipalityId) {
                echo "      ✅ Mapeo exitoso\n";
            } else {
                echo "      ❌ Mapeo fallido\n";
                if (!$stateId) echo "         - Estado no encontrado: {$data['state_name']}\n";
                if (!$municipalityId) echo "         - Municipio no encontrado: {$municipalityKey}\n";
            }
            echo "\n";
        }
        
        fclose($handle);
    }
    
    echo "4️⃣  Resumen y recomendaciones:\n";
    echo "   📊 Total de variaciones de estados: " . count($stateMap) . "\n";
    echo "   📊 Total de variaciones de municipios: " . count($municipalityMap) . "\n";
    
    echo "\n   💡 Recomendaciones:\n";
    echo "      1. Si hay errores, ejecuta el diagnóstico completo: php diagnose_voting_centers.php\n";
    echo "      2. Asegúrate de que UbicacionesSeeder esté ejecutado\n";
    echo "      3. Ejecuta VotingCentersSeeder con debugging habilitado\n\n";
    
    echo "✅ Prueba completada\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 Archivo: " . $e->getFile() . "\n";
    echo "📍 Línea: " . $e->getLine() . "\n\n";
    exit(1);
}

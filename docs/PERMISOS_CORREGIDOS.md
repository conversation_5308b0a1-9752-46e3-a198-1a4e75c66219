# Corrección y Actualización de Permisos del Sistema

## ✅ CORRECCIÓN COMPLETADA

Se han corregido y actualizado completamente los permisos del sistema, eliminando todas las inconsistencias en español y estableciendo un sistema unificado en inglés siguiendo las convenciones de Laravel.

## Resumen de Cambios

Se han corregido y actualizado los permisos del sistema para incluir una gestión completa de centros de votación y mejorar la seguridad del sistema.

## ✅ Permisos Corregidos (Español → Inglés)

### Gestión de Personas
- `view personas` → `view people`
- `create personas` → `create people`
- `update personas` → `update people`
- `delete personas` → `delete people`
- `export personas` → `export people`
- `import personas` → `import people`
- `assign lider personas` → `assign people leaders`
- `create user from persona` → `create user from person`
- `view own personas` → `view own people`

### Ubicaciones Geográficas
- `view ubicaciones` → `view locations`
- `create ubicaciones` → `create locations`
- `update ubicaciones` → `update locations`
- `delete ubicaciones` → `delete locations`
- `export ubicaciones` → `export locations`
- `import ubicaciones` → `import locations`

### Eventos Electorales
- `view eventos_electorales` → `view electoral_events`
- `create eventos_electorales` → `create electoral_events`
- `update eventos_electorales` → `update electoral_events`
- `delete eventos_electorales` → `delete electoral_events`
- `export eventos_electorales` → `export electoral_events`

### Movilizaciones
- `view movilizaciones` → `view mobilizations`
- `create movilizaciones` → `create mobilizations`
- `update movilizaciones` → `update mobilizations`
- `delete movilizaciones` → `delete mobilizations`
- `export movilizaciones` → `export mobilizations`

## Nuevos Permisos Agregados

### Centros de Votación
- `view voting_centers` - Ver centros de votación
- `create voting_centers` - Crear centros de votación
- `update voting_centers` - Actualizar centros de votación
- `delete voting_centers` - Eliminar centros de votación
- `export voting_centers` - Exportar centros de votación
- `import voting_centers` - Importar centros de votación
- `search voting_centers` - Búsqueda avanzada de centros
- `view voting_center_stats` - Ver estadísticas de centros

### Mesas Electorales
- `view voting_tables` - Ver mesas electorales
- `create voting_tables` - Crear mesas electorales
- `update voting_tables` - Actualizar mesas electorales
- `delete voting_tables` - Eliminar mesas electorales

### Ubicaciones Geográficas (Ampliados)
- `export ubicaciones` - Exportar ubicaciones
- `import ubicaciones` - Importar ubicaciones

### Gestión de Personas (Ampliados)
- `view own personas` - Ver solo personas del propio grupo (para líderes 1x10)

### Reportes y Estadísticas
- `view reports` - Ver reportes
- `create reports` - Crear reportes
- `export reports` - Exportar reportes
- `view dashboard_stats` - Ver estadísticas del dashboard

### Configuración del Sistema
- `view system_config` - Ver configuración del sistema
- `update system_config` - Actualizar configuración
- `view system_logs` - Ver logs del sistema
- `clear system_cache` - Limpiar caché del sistema

### Auditoría
- `view audit_logs` - Ver logs de auditoría
- `export audit_logs` - Exportar logs de auditoría

## Roles Actualizados

### Super Admin
- Mantiene todos los permisos del sistema

### Coordinador de Personas
- Agregados permisos de centros de votación (view, search, stats)
- Agregados permisos de reportes y estadísticas

### Líder 1x10
- Agregado permiso específico `view own personas`
- Agregados permisos de ubicaciones y centros de votación (solo lectura)
- Agregado acceso a estadísticas del dashboard

### Militante
- Agregados permisos de ubicaciones y centros de votación (solo lectura)

### Votante
- Agregados permisos básicos de centros de votación (view, search)

### Operador de Datos
- Agregados permisos de centros de votación (solo lectura)
- Agregado permiso de exportación de personas

## Nuevos Roles Creados

### Coordinador Electoral
- Gestión completa de centros de votación y mesas electorales
- Gestión de eventos electorales
- Acceso a reportes y estadísticas
- Permisos de importación/exportación

### Analista de Datos
- Acceso de solo lectura a todos los módulos
- Permisos completos de reportes y exportación
- Acceso a estadísticas y análisis

## Seguridad Implementada

### VotingCenterController
- Agregado middleware de autenticación
- Agregadas verificaciones de permisos específicos para cada acción:
  - `view voting_centers` para consultas
  - `create voting_centers` para creación
  - `update voting_centers` para actualizaciones
  - `delete voting_centers` para eliminación
  - `search voting_centers` para búsquedas avanzadas
  - `view voting_center_stats` para estadísticas
  - `export voting_centers` para exportación

## Comandos para Aplicar los Cambios

```bash
# Limpiar permisos en español (EJECUTADO)
php artisan permissions:cleanup

# Aplicar nuevos permisos (EJECUTADO)
php artisan db:seed --class=PermissionSeeder

# Actualizar roles con nuevos permisos (EJECUTADO)
php artisan db:seed --class=RoleSeeder

# Verificar permisos creados
php artisan tinker --execute="echo \Spatie\Permission\Models\Permission::count() . ' permisos creados';"

# Verificar roles creados
php artisan tinker --execute="echo \Spatie\Permission\Models\Role::count() . ' roles creados';"

# Verificar que no hay permisos en español
php artisan permissions:cleanup --dry-run
```

## ✅ Verificación del Sistema

Después de aplicar los cambios:

1. **61 permisos** han sido creados en total (actualizados y corregidos)
2. **8 roles** están configurados en el sistema
3. **Todos los permisos están en inglés** siguiendo convenciones de Laravel
4. **Eliminadas todas las inconsistencias** en español (19 permisos eliminados)
5. Todos los controladores tienen middleware de autorización apropiado
6. Los permisos están organizados por módulos para fácil gestión
7. **VotingCenterController** tiene autorización completa implementada
8. **Comando de limpieza** creado para mantener consistencia

## Próximos Pasos

1. Asignar roles apropiados a los usuarios existentes
2. Probar el acceso a las funcionalidades según los roles
3. Implementar middleware de autorización en componentes Livewire si es necesario
4. Documentar cualquier permiso adicional que se requiera para nuevas funcionalidades

## Notas Importantes

- Los permisos se aplican usando el paquete Spatie Laravel Permission
- Los roles son jerárquicos: Super Admin > Coordinadores > Líderes > Militantes > Votantes
- El permiso `view own people` permite a los líderes 1x10 ver solo su grupo asignado
- Todos los permisos de exportación requieren autenticación adicional por seguridad
- **Comando de limpieza disponible**: `php artisan permissions:cleanup` para mantener consistencia

## Comando de Limpieza de Permisos

Se ha creado un comando personalizado para limpiar permisos en español y mantener la consistencia:

```bash
# Verificar permisos en español sin eliminar
php artisan permissions:cleanup --dry-run

# Eliminar permisos en español
php artisan permissions:cleanup
```

**Características del comando:**
- ✅ Detecta automáticamente permisos en español
- ✅ Remueve permisos de roles antes de eliminarlos
- ✅ Modo dry-run para verificación segura
- ✅ Reporta estadísticas detalladas
- ✅ Sugiere comandos de recuperación

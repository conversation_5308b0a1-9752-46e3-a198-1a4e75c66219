# Módulo de Captación de Votantes - Estrategia 1x10

## Descripción

El módulo de Captación de Votantes implementa la estrategia 1x10 para el registro, gestión y monitoreo del proceso de captación de votantes en un sistema electoral. Permite que cada responsable (líder 1x10) pueda gestionar hasta 10 votantes bajo su responsabilidad, con un sistema completo de seguimiento y notificaciones.

## Características Principales

### ✅ Funcionalidades Implementadas

1. **Registro de Votantes**
   - Formulario completo con datos personales, contacto y ubicación
   - Información electoral (centro de votación, mesa)
   - Asignación automática a responsables (líderes 1x10)
   - Validaciones de datos y prevención de duplicados

2. **Gestión de Estados de Captación**
   - **Pendiente**: Captación recién registrada
   - **Contactado**: Se ha establecido contacto inicial
   - **Confirmado**: Votante confirmó su participación
   - **No Interesado**: Votante no está interesado
   - **No Localizable**: No se puede contactar al votante

3. **Sistema de Seguimiento**
   - Fechas de contacto y próximo contacto
   - Contador de intentos de contacto
   - Notas detalladas de cada interacción
   - Niveles de interés y compromiso

4. **Notificaciones y Alertas**
   - Recordatorios automáticos por email
   - Notificaciones en base de datos
   - Alertas para captaciones vencidas
   - Sistema programado de recordatorios

5. **Conversión a Personas**
   - Conversión automática de captaciones confirmadas
   - Integración con el módulo de Gestión de Personas
   - Mantenimiento de trazabilidad

6. **Estadísticas y Reportes**
   - Dashboard con métricas clave
   - Tasas de conversión y confirmación
   - Rendimiento por responsable
   - Captaciones por estado

## Estructura de Base de Datos

### Tabla: `voter_captures`

```sql
- id (Primary Key)
- first_name, last_name, document_number (Datos personales)
- birth_date, gender (Información demográfica)
- phone, secondary_phone, email, address (Contacto)
- state_id, municipality_id, parish_id (Ubicación)
- voting_center_id, voting_table (Electoral)
- responsible_id (Responsable asignado)
- capture_status (Estado de captación)
- capture_date, last_contact_date, next_contact_date
- contact_notes, capture_source, contact_method
- contact_attempts, interest_level, commitment_level
- willing_to_vote, willing_to_mobilize
- needs_follow_up, follow_up_date
- reminder_sent, last_reminder_sent
- person_id, converted_to_person, conversion_date
- status, notes, additional_data
- timestamps
```

## Rutas Disponibles

```php
// Gestión de Captaciones
GET /admin/voter-captures                    - Listado de captaciones
GET /admin/voter-captures/create             - Crear captación
GET /admin/voter-captures/{capture}          - Ver detalles
GET /admin/voter-captures/{capture}/edit     - Editar captación
```

## Componentes Livewire

1. **App\Livewire\Admin\VoterCaptures\Pages\VoterCaptures** - Listado principal
2. **App\Livewire\Admin\VoterCaptures\Pages\CreateVoterCapture** - Crear captación
3. **App\Livewire\Admin\VoterCaptures\Pages\ViewVoterCapture** - Ver detalles
4. **App\Livewire\Admin\VoterCaptures\Pages\EditVoterCapture** - Editar captación
5. **App\Livewire\Admin\VoterCaptures\Components\CaptureStats** - Estadísticas

## Modelos Principales

1. **App\Models\VoterCapture** - Modelo principal de captaciones
2. **App\Jobs\SendCaptureReminderJob** - Job para envío de recordatorios
3. **App\Notifications\CaptureReminderNotification** - Notificación de recordatorio

## Sistema de Permisos

### Permisos Disponibles

- `view captures` - Ver captaciones
- `create captures` - Crear captaciones
- `update captures` - Actualizar captaciones
- `delete captures` - Eliminar captaciones
- `manage captures` - Gestionar captaciones
- `view capture stats` - Ver estadísticas
- `export captures` - Exportar datos
- `import captures` - Importar datos
- `convert captures to people` - Convertir a personas
- `send capture reminders` - Enviar recordatorios
- `view all captures` - Ver todas las captaciones
- `view own captures` - Ver solo captaciones propias

### Asignación por Roles

- **Super Admin**: Todos los permisos
- **Coordinador de Personas**: Gestión completa
- **Líder 1x10**: Gestión de sus propias captaciones
- **Operador de Datos**: Operaciones básicas
- **Militante**: Ver y crear captaciones
- **Votante**: Solo visualización básica

## Sistema de Notificaciones

### Tipos de Notificaciones

1. **Recordatorios de Seguimiento**
   - Enviados automáticamente cuando una captación requiere seguimiento
   - Configurables por horario y frecuencia
   - Incluyen detalles del votante y responsable

2. **Alertas de Captaciones Vencidas**
   - Para captaciones que han pasado su fecha de próximo contacto
   - Resaltan la urgencia del seguimiento

### Configuración

```php
// config/voter_captures.php
'reminders' => [
    'enabled' => true,
    'send_time' => '09:00',
    'frequency_hours' => 24,
    'channels' => ['mail', 'database'],
]
```

## Comandos Artisan

### `captures:send-reminders`

Envía recordatorios automáticos para captaciones que requieren seguimiento.

```bash
# Enviar recordatorios
php artisan captures:send-reminders

# Modo de prueba (no envía realmente)
php artisan captures:send-reminders --dry-run

# Forzar envío (ignora restricciones de tiempo)
php artisan captures:send-reminders --force
```

### Programación Automática

El comando se ejecuta automáticamente todos los días a las 9:00 AM:

```php
// routes/console.php
Schedule::command('captures:send-reminders')
    ->dailyAt('09:00')
    ->when(function () {
        return config('voter_captures.reminders.enabled', true);
    });
```

## Flujo de Trabajo

### 1. Registro de Captación

1. Responsable registra nuevo votante
2. Se asigna estado "Pendiente"
3. Se programa fecha de próximo contacto
4. Sistema envía notificación de nueva captación

### 2. Proceso de Contacto

1. Responsable contacta al votante
2. Actualiza estado según respuesta
3. Registra notas del contacto
4. Programa próximo seguimiento si es necesario

### 3. Confirmación

1. Votante confirma participación
2. Estado cambia a "Confirmado"
3. Se puede convertir a persona en el sistema
4. Se archiva la captación

### 4. Seguimiento Automático

1. Sistema verifica captaciones vencidas
2. Envía recordatorios a responsables
3. Genera alertas en dashboard
4. Actualiza estadísticas

## Estadísticas y Métricas

### Métricas Principales

- **Total de Captaciones**: Número total registrado
- **Tasa de Confirmación**: % de captaciones confirmadas
- **Tasa de Conversión**: % convertidas a personas
- **Captaciones Vencidas**: Requieren atención inmediata
- **Rendimiento por Responsable**: Comparativo de efectividad

### Dashboard

El componente `CaptureStats` proporciona:

- Resumen visual de estadísticas
- Gráficos de distribución por estado
- Ranking de responsables más efectivos
- Acciones rápidas contextuales

## Configuración e Instalación

### 1. Ejecutar Migraciones

```bash
php artisan migrate
```

### 2. Ejecutar Seeders de Permisos

```bash
php artisan db:seed --class=VoterCapturePermissionSeeder
```

### 3. Configurar Queue Worker

```bash
# Para procesamiento de notificaciones
php artisan queue:work
```

### 4. Configurar Cron Job

```bash
# Agregar al crontab para recordatorios automáticos
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## Integración con Otros Módulos

### Módulo de Personas

- Conversión automática de captaciones confirmadas
- Asignación de líderes 1x10 como responsables
- Sincronización de datos personales y de contacto

### Sistema de Ubicaciones

- Utiliza estados, municipios y parroquias existentes
- Integración con centros de votación
- Filtros geográficos en listados

### Sistema de Usuarios

- Notificaciones por email y base de datos
- Permisos granulares por rol
- Auditoría de acciones

## Mejores Prácticas

### Para Responsables

1. **Contacto Regular**: Mantener comunicación constante
2. **Registro Detallado**: Documentar todas las interacciones
3. **Seguimiento Oportuno**: Respetar fechas programadas
4. **Actualización de Estados**: Mantener información actualizada

### Para Coordinadores

1. **Monitoreo de Métricas**: Revisar dashboard regularmente
2. **Apoyo a Responsables**: Identificar necesidades de capacitación
3. **Análisis de Tendencias**: Usar estadísticas para mejoras
4. **Gestión de Alertas**: Atender captaciones vencidas

## Solución de Problemas

### Recordatorios No Se Envían

1. Verificar configuración en `config/voter_captures.php`
2. Confirmar que el queue worker está ejecutándose
3. Revisar logs en `storage/logs/laravel.log`
4. Verificar configuración de email

### Permisos Insuficientes

1. Verificar asignación de roles al usuario
2. Ejecutar seeder de permisos si es necesario
3. Limpiar cache de permisos: `php artisan permission:cache-reset`

### Problemas de Rendimiento

1. Verificar índices en base de datos
2. Configurar cache para estadísticas
3. Optimizar consultas con eager loading
4. Considerar paginación en listados grandes

## Roadmap Futuro

### Funcionalidades Planificadas

1. **Importación Masiva**: Carga de captaciones desde archivos
2. **Exportación de Reportes**: Generación de reportes en PDF/Excel
3. **API REST**: Endpoints para integración externa
4. **App Móvil**: Aplicación para responsables en campo
5. **Geolocalización**: Mapas de captaciones por zona
6. **Análisis Predictivo**: IA para identificar mejores prospectos

### Mejoras Técnicas

1. **Cache Avanzado**: Optimización de consultas frecuentes
2. **Eventos y Listeners**: Arquitectura más desacoplada
3. **Tests Automatizados**: Cobertura completa de funcionalidades
4. **Documentación API**: Swagger/OpenAPI
5. **Monitoreo**: Métricas de rendimiento y uso

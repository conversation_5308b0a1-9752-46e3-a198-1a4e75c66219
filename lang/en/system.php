<?php

return [
    'title' => 'System',
    'title_description' => 'Electoral system configuration and administration',

    // General settings
    'general_settings' => 'General Settings',
    'system_name' => 'System Name',
    'system_description' => 'System Description',
    'system_version' => 'System Version',
    'system_environment' => 'System Environment',
    'maintenance_mode' => 'Maintenance Mode',
    'debug_mode' => 'Debug Mode',
    'timezone' => 'Timezone',
    'default_language' => 'Default Language',
    'date_format' => 'Date Format',
    'time_format' => 'Time Format',

    // Security settings
    'security_settings' => 'Security Settings',
    'password_policy' => 'Password Policy',
    'min_password_length' => 'Minimum Password Length',
    'require_uppercase' => 'Require Uppercase',
    'require_lowercase' => 'Require Lowercase',
    'require_numbers' => 'Require Numbers',
    'require_symbols' => 'Require Symbols',
    'password_expiry' => 'Password Expiry',
    'session_timeout' => 'Session Timeout',
    'max_login_attempts' => 'Maximum Login Attempts',
    'lockout_duration' => 'Lockout Duration',
    'two_factor_auth' => 'Two Factor Authentication',

    // Email settings
    'email_settings' => 'Email Settings',
    'mail_driver' => 'Mail Driver',
    'smtp_host' => 'SMTP Host',
    'smtp_port' => 'SMTP Port',
    'smtp_username' => 'SMTP Username',
    'smtp_password' => 'SMTP Password',
    'smtp_encryption' => 'SMTP Encryption',
    'from_email' => 'From Email',
    'from_name' => 'From Name',
    'test_email' => 'Test Email',

    // Database settings
    'database_settings' => 'Database Settings',
    'database_connection' => 'Database Connection',
    'database_host' => 'Database Host',
    'database_port' => 'Database Port',
    'database_name' => 'Database Name',
    'database_username' => 'Database Username',
    'database_password' => 'Database Password',
    'database_charset' => 'Database Charset',
    'database_collation' => 'Database Collation',

    // File settings
    'file_settings' => 'File Settings',
    'upload_path' => 'Upload Path',
    'max_file_size' => 'Maximum File Size',
    'allowed_file_types' => 'Allowed File Types',
    'storage_driver' => 'Storage Driver',
    'disk_quota' => 'Disk Quota',
    'auto_cleanup' => 'Auto Cleanup',
    'backup_frequency' => 'Backup Frequency',

    // Cache settings
    'cache_settings' => 'Cache Settings',
    'cache_driver' => 'Cache Driver',
    'cache_ttl' => 'Cache TTL',
    'cache_prefix' => 'Cache Prefix',
    'redis_host' => 'Redis Host',
    'redis_port' => 'Redis Port',
    'redis_password' => 'Redis Password',
    'memcached_host' => 'Memcached Host',
    'memcached_port' => 'Memcached Port',

    // Logging settings
    'logging_settings' => 'Logging Settings',
    'log_level' => 'Log Level',
    'log_channel' => 'Log Channel',
    'log_path' => 'Log Path',
    'log_rotation' => 'Log Rotation',
    'max_log_files' => 'Maximum Log Files',
    'log_retention' => 'Log Retention',
    'error_reporting' => 'Error Reporting',

    // Notification settings
    'notification_settings' => 'Notification Settings',
    'enable_notifications' => 'Enable Notifications',
    'email_notifications' => 'Email Notifications',
    'sms_notifications' => 'SMS Notifications',
    'push_notifications' => 'Push Notifications',
    'notification_frequency' => 'Notification Frequency',
    'notification_templates' => 'Notification Templates',

    // API settings
    'api_settings' => 'API Settings',
    'api_enabled' => 'API Enabled',
    'api_version' => 'API Version',
    'api_rate_limit' => 'API Rate Limit',
    'api_authentication' => 'API Authentication',
    'api_documentation' => 'API Documentation',
    'cors_enabled' => 'CORS Enabled',
    'allowed_origins' => 'Allowed Origins',

    // System maintenance
    'system_maintenance' => 'System Maintenance',
    'clear_cache' => 'Clear Cache',
    'clear_logs' => 'Clear Logs',
    'optimize_database' => 'Optimize Database',
    'backup_database' => 'Backup Database',
    'restore_database' => 'Restore Database',
    'system_update' => 'System Update',
    'migration_status' => 'Migration Status',
    'queue_status' => 'Queue Status',

    // System monitoring
    'system_monitoring' => 'System Monitoring',
    'system_status' => 'System Status',
    'server_info' => 'Server Information',
    'php_version' => 'PHP Version',
    'laravel_version' => 'Laravel Version',
    'memory_usage' => 'Memory Usage',
    'disk_usage' => 'Disk Usage',
    'cpu_usage' => 'CPU Usage',
    'uptime' => 'Uptime',
    'last_backup' => 'Last Backup',

    // Audit and logs
    'audit_logs' => 'Audit Logs',
    'system_logs' => 'System Logs',
    'error_logs' => 'Error Logs',
    'access_logs' => 'Access Logs',
    'security_logs' => 'Security Logs',
    'view_logs' => 'View Logs',
    'download_logs' => 'Download Logs',
    'clear_logs' => 'Clear Logs',

    // System users and permissions
    'system_users' => 'System Users',
    'active_sessions' => 'Active Sessions',
    'user_activity' => 'User Activity',
    'permission_matrix' => 'Permission Matrix',
    'role_assignments' => 'Role Assignments',
    'access_control' => 'Access Control',

    // Module settings
    'module_settings' => 'Module Settings',
    'enabled_modules' => 'Enabled Modules',
    'disabled_modules' => 'Disabled Modules',
    'module_permissions' => 'Module Permissions',
    'module_configuration' => 'Module Configuration',

    // System actions
    'save_settings' => 'Save Settings',
    'reset_settings' => 'Reset Settings',
    'export_settings' => 'Export Settings',
    'import_settings' => 'Import Settings',
    'test_connection' => 'Test Connection',
    'validate_settings' => 'Validate Settings',
    'apply_changes' => 'Apply Changes',
    'restart_services' => 'Restart Services',

    // System status
    'online' => 'Online',
    'offline' => 'Offline',
    'maintenance' => 'Under Maintenance',
    'updating' => 'Updating',
    'healthy' => 'Healthy',
    'warning' => 'Warning',
    'critical' => 'Critical',
    'unknown' => 'Unknown',

    // System messages
    'settings_saved' => 'Settings saved successfully',
    'settings_reset' => 'Settings reset successfully',
    'cache_cleared' => 'Cache cleared successfully',
    'logs_cleared' => 'Logs cleared successfully',
    'database_optimized' => 'Database optimized successfully',
    'backup_created' => 'Backup created successfully',
    'backup_restored' => 'Backup restored successfully',
    'connection_successful' => 'Connection successful',
    'connection_failed' => 'Connection failed',
    'invalid_configuration' => 'Invalid configuration',
    'permission_denied' => 'Permission denied',
    'operation_completed' => 'Operation completed',
    'operation_failed' => 'Operation failed',

    // Confirmations
    'confirm_reset' => 'Confirm Reset',
    'confirm_clear_cache' => 'Confirm Clear Cache',
    'confirm_clear_logs' => 'Confirm Clear Logs',
    'confirm_backup' => 'Confirm Backup',
    'confirm_restore' => 'Confirm Restore',
    'action_irreversible' => 'This action cannot be undone',

    // System permissions
    'view_system_config' => 'View System Configuration',
    'update_system_config' => 'Update System Configuration',
    'view_system_logs' => 'View System Logs',
    'clear_system_cache' => 'Clear System Cache',
    'backup_system' => 'Backup System',
    'restore_system' => 'Restore System',
    'monitor_system' => 'Monitor System',
    'manage_system_users' => 'Manage System Users',
];

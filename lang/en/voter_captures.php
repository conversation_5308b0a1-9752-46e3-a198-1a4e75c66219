<?php

return [
    'title' => 'Voter Captures',
    'title_description' => 'Manage the voter capture process through the 1x10 strategy',

    // Navigation and menus
    'listing' => 'Listing',
    'add_capture' => 'Add Capture',
    'view_capture' => 'View Capture',
    'edit_capture' => 'Edit Capture',
    'capture_stats' => 'Capture Statistics',

    // Tabs and filters
    'all' => 'All',
    'pending' => 'Pending',
    'contacted' => 'Contacted',
    'confirmed' => 'Confirmed',
    'not_interested' => 'Not Interested',
    'unreachable' => 'Unreachable',
    'overdue' => 'Overdue',
    'needs_follow_up' => 'Needs Follow-up',

    // Form fields - Personal data
    'personal_data' => 'Personal Data',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',

    // Contact information
    'contact_information' => 'Contact Information',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Electoral information
    'electoral_information' => 'Electoral Information',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',
    'select_voting_center' => 'Select Voting Center',

    // Capture management
    'capture_management' => 'Capture Management',
    'responsible' => 'Responsible',
    'capture_status' => 'Capture Status',
    'capture_date' => 'Capture Date',
    'last_contact_date' => 'Last Contact Date',
    'next_contact_date' => 'Next Contact Date',
    'contact_notes' => 'Contact Notes',
    'select_responsible' => 'Select Responsible',

    // Capture source and method
    'capture_details' => 'Capture Details',
    'capture_source' => 'Capture Source',
    'contact_method' => 'Contact Method',
    'contact_attempts' => 'Contact Attempts',

    // Capture sources
    'door_to_door' => 'Door to Door',
    'phone_call' => 'Phone Call',
    'social_media' => 'Social Media',
    'event' => 'Event',
    'referral' => 'Referral',
    'other_source' => 'Other',

    // Contact methods
    'phone_contact' => 'Phone',
    'whatsapp' => 'WhatsApp',
    'visit' => 'Visit',
    'email_contact' => 'Email',
    'social_media_contact' => 'Social Media',

    // Interest and commitment
    'interest_commitment' => 'Interest and Commitment',
    'interest_level' => 'Interest Level',
    'commitment_level' => 'Commitment Level',
    'willing_to_vote' => 'Willing to Vote',
    'willing_to_mobilize' => 'Willing to Mobilize',

    // Interest levels
    'high_interest' => 'High',
    'medium_interest' => 'Medium',
    'low_interest' => 'Low',
    'no_interest' => 'None',

    // Commitment levels
    'committed' => 'Committed',
    'likely' => 'Likely',
    'uncertain' => 'Uncertain',
    'unlikely' => 'Unlikely',

    // Follow-up
    'follow_up' => 'Follow-up',
    'needs_follow_up_field' => 'Needs Follow-up',
    'follow_up_date' => 'Follow-up Date',
    'reminder_sent' => 'Reminder Sent',
    'last_reminder_sent' => 'Last Reminder Sent',

    // Conversion
    'conversion' => 'Conversion',
    'converted_to_person' => 'Converted to Person',
    'conversion_date' => 'Conversion Date',
    'convert_to_person' => 'Convert to Person',

    // Status
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'archived' => 'Archived',
    'notes' => 'Notes',

    // Actions
    'actions' => 'Actions',
    'view' => 'View',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'mark_as_contacted' => 'Mark as Contacted',
    'mark_as_confirmed' => 'Mark as Confirmed',
    'schedule_follow_up' => 'Schedule Follow-up',
    'send_reminder' => 'Send Reminder',

    // Statistics
    'total_captures' => 'Total Captures',
    'pending_captures' => 'Pending Captures',
    'confirmed_captures' => 'Confirmed Captures',
    'conversion_rate' => 'Conversion Rate',
    'overdue_captures' => 'Overdue Captures',
    'captures_this_month' => 'Captures This Month',
    'captures_by_responsible' => 'Captures by Responsible',
    'captures_by_status' => 'Captures by Status',

    // Priority levels
    'low_priority' => 'Low',
    'medium_priority' => 'Medium',
    'high_priority' => 'High',
    'urgent_priority' => 'Urgent',

    // Additional fields
    'source_details' => 'Source Details',
    'campaign_id' => 'Campaign ID',
    'referral_code' => 'Referral Code',
    'quality_score' => 'Quality Score',
    'update_capture' => 'Update Capture',

    // Messages
    'capture_created' => 'Capture created successfully',
    'capture_updated' => 'Capture updated successfully',
    'capture_deleted' => 'Capture deleted successfully',
    'capture_contacted' => 'Capture marked as contacted',
    'capture_confirmed' => 'Capture confirmed successfully',
    'person_converted' => 'Capture converted to person successfully',
    'reminder_sent' => 'Reminder sent successfully',

    // Errors
    'error_creating_capture' => 'Error creating capture',
    'error_updating_capture' => 'Error updating capture',
    'error_deleting_capture' => 'Error deleting capture',
    'error_converting_person' => 'Error converting to person',
    'document_already_exists' => 'A capture with this document already exists',
    'responsible_not_found' => 'Responsible not found',

    // Confirmations
    'confirm_delete' => 'Are you sure you want to delete this capture?',
    'confirm_convert' => 'Are you sure you want to convert this capture to person?',
    'confirm_mark_contacted' => 'Mark as contacted?',
    'confirm_mark_confirmed' => 'Confirm this capture?',

    // Search and filters
    'search' => 'Search',
    'search_placeholder' => 'Search by name, document, phone...',
    'filter_by_status' => 'Filter by Status',
    'filter_by_responsible' => 'Filter by Responsible',
    'filter_by_location' => 'Filter by Location',
    'filter_by_date' => 'Filter by Date',
    'clear_filters' => 'Clear Filters',

    // Dates
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'days_ago' => ':days days ago',
    'days_from_now' => 'in :days days',

    // Validation
    'required_field' => 'This field is required',
    'invalid_document' => 'Invalid document number',
    'invalid_phone' => 'Invalid phone number',
    'invalid_email' => 'Invalid email address',
    'invalid_date' => 'Invalid date',

    // Permissions
    'view_captures' => 'View Captures',
    'create_captures' => 'Create Captures',
    'update_captures' => 'Update Captures',
    'delete_captures' => 'Delete Captures',
    'manage_captures' => 'Manage Captures',
    'view_capture_stats' => 'View Capture Statistics',

    // Reminder notifications
    'reminder_subject' => 'Voter Capture Reminder',
    'reminder_greeting' => 'Hello :name,',
    'reminder_message' => 'You have a pending follow-up for voter capture :voter_name (ID: :document).',
    'reminder_footer' => 'Remember to maintain regular contact with captured voters to ensure their participation.',
    'reminder_database_message' => 'Capture for :voter_name needs follow-up',
    'no_contact_details' => 'No contact information available',
    'days_overdue' => ':days days overdue',
];

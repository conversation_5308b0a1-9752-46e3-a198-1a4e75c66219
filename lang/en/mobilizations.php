<?php

return [
    'title' => 'Mobilizations',
    'title_description' => 'Management of events, marches, rallies and mobilization activities',

    // Navigation and menus
    'listing' => 'Listing',
    'add_mobilization' => 'Add Mobilization',
    'mobilization_details' => 'Mobilization Details',
    'edit_mobilization' => 'Edit Mobilization',
    'manage_participants' => 'Manage Participants',
    'manage_logistics' => 'Manage Logistics',

    // Mobilization types
    'mobilization_type' => 'Mobilization Type',
    'march' => 'March',
    'rally' => 'Rally',
    'meeting' => 'Meeting',
    'assembly' => 'Assembly',
    'protest' => 'Protest',
    'celebration' => 'Celebration',
    'campaign_event' => 'Campaign Event',
    'community_activity' => 'Community Activity',
    'training' => 'Training',
    'other' => 'Other',

    // Basic information
    'mobilization_name' => 'Mobilization Name',
    'mobilization_code' => 'Mobilization Code',
    'description' => 'Description',
    'objective' => 'Objective',
    'event_date' => 'Event Date',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'duration' => 'Duration',
    'expected_attendance' => 'Expected Attendance',
    'actual_attendance' => 'Actual Attendance',

    // Event location
    'event_location' => 'Event Location',
    'venue' => 'Venue',
    'address' => 'Address',
    'city' => 'City',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'coordinates' => 'Coordinates',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'meeting_point' => 'Meeting Point',
    'route' => 'Route',
    'final_destination' => 'Final Destination',

    // Mobilization status
    'status' => 'Status',
    'planned' => 'Planned',
    'confirmed' => 'Confirmed',
    'in_progress' => 'In Progress',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'postponed' => 'Postponed',
    'suspended' => 'Suspended',

    // Organization
    'organization' => 'Organization',
    'organizer' => 'Organizer',
    'coordinator' => 'Coordinator',
    'responsible_person' => 'Responsible Person',
    'contact_phone' => 'Contact Phone',
    'contact_email' => 'Contact Email',
    'organizing_committee' => 'Organizing Committee',
    'sponsors' => 'Sponsors',
    'collaborators' => 'Collaborators',

    // Participants
    'participants' => 'Participants',
    'registered_participants' => 'Registered Participants',
    'confirmed_participants' => 'Confirmed Participants',
    'attendance_list' => 'Attendance List',
    'participant_name' => 'Participant Name',
    'participant_role' => 'Participant Role',
    'registration_date' => 'Registration Date',
    'confirmation_status' => 'Confirmation Status',
    'add_participant' => 'Add Participant',
    'remove_participant' => 'Remove Participant',

    // Logistics
    'logistics' => 'Logistics',
    'transportation' => 'Transportation',
    'accommodation' => 'Accommodation',
    'catering' => 'Catering',
    'security' => 'Security',
    'medical_support' => 'Medical Support',
    'equipment' => 'Equipment',
    'materials' => 'Materials',
    'budget' => 'Budget',
    'estimated_cost' => 'Estimated Cost',
    'actual_cost' => 'Actual Cost',

    // Required resources
    'required_resources' => 'Required Resources',
    'sound_system' => 'Sound System',
    'stage' => 'Stage',
    'chairs' => 'Chairs',
    'tables' => 'Tables',
    'banners' => 'Banners',
    'flags' => 'Flags',
    'promotional_materials' => 'Promotional Materials',
    'refreshments' => 'Refreshments',
    'first_aid_kit' => 'First Aid Kit',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'confirm_event' => 'Confirm Event',
    'start_event' => 'Start Event',
    'finish_event' => 'Finish Event',
    'export' => 'Export',
    'import' => 'Import',
    'generate_report' => 'Generate Report',
    'send_invitations' => 'Send Invitations',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name or code',
    'filter_by_type' => 'Filter by type',
    'filter_by_status' => 'Filter by status',
    'filter_by_date' => 'Filter by date',
    'filter_by_location' => 'Filter by location',
    'filter_by_organizer' => 'Filter by organizer',
    'advanced_search' => 'Advanced Search',

    // Statistics
    'statistics' => 'Statistics',
    'total_mobilizations' => 'Total Mobilizations',
    'active_mobilizations' => 'Active Mobilizations',
    'completed_mobilizations' => 'Completed Mobilizations',
    'upcoming_mobilizations' => 'Upcoming Mobilizations',
    'mobilizations_by_type' => 'Mobilizations by Type',
    'mobilizations_by_month' => 'Mobilizations by Month',
    'average_attendance' => 'Average Attendance',
    'total_participants' => 'Total Participants',

    // Messages
    'mobilization_created' => 'Mobilization created successfully',
    'mobilization_updated' => 'Mobilization updated successfully',
    'mobilization_deleted' => 'Mobilization deleted successfully',
    'participant_added' => 'Participant added successfully',
    'participant_removed' => 'Participant removed successfully',
    'invitations_sent' => 'Invitations sent successfully',
    'event_confirmed' => 'Event confirmed successfully',
    'event_started' => 'Event started successfully',
    'event_finished' => 'Event finished successfully',
    'error_creating_mobilization' => 'Error creating mobilization',
    'error_updating_mobilization' => 'Error updating mobilization',
    'error_deleting_mobilization' => 'Error deleting mobilization',
    'mobilization_code_exists' => 'A mobilization with this code already exists',
    'no_results' => 'No mobilizations found with applied filters',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'confirm_delete_mobilization' => 'Are you sure you want to delete this mobilization?',
    'confirm_remove_participant' => 'Are you sure you want to remove this participant?',
    'confirm_cancel_event' => 'Are you sure you want to cancel this event?',
    'action_irreversible' => 'This action cannot be undone.',
    'event_has_participants' => 'This event has registered participants.',
    'event_is_active' => 'This event is active.',

    // Validations
    'mobilization_name_required' => 'Mobilization name is required',
    'mobilization_code_required' => 'Mobilization code is required',
    'mobilization_type_required' => 'Mobilization type is required',
    'event_date_required' => 'Event date is required',
    'event_date_future' => 'Event date must be in the future',
    'start_time_required' => 'Start time is required',
    'end_time_required' => 'End time is required',
    'end_time_after_start' => 'End time must be after start time',
    'venue_required' => 'Event venue is required',
    'organizer_required' => 'Organizer is required',
    'expected_attendance_numeric' => 'Expected attendance must be a number',

    // Export and import
    'export_excel' => 'Export to Excel',
    'export_pdf' => 'Export to PDF',
    'export_csv' => 'Export to CSV',
    'import_mobilizations' => 'Import Mobilizations',
    'import_participants' => 'Import Participants',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    'file_formats' => 'Allowed formats: Excel (.xlsx), CSV (.csv)',
    'export_successful' => 'Export completed successfully',
    'import_successful' => 'Import completed successfully',
    'import_errors' => 'Errors found during import',

    // Additional information
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated',
    'created_by' => 'Created by',
    'updated_by' => 'Updated by',
    'weather_conditions' => 'Weather Conditions',
    'media_coverage' => 'Media Coverage',
    'social_media' => 'Social Media',
    'hashtags' => 'Hashtags',

    // Reports
    'reports' => 'Reports',
    'mobilization_report' => 'Mobilization Report',
    'participants_report' => 'Participants Report',
    'attendance_report' => 'Attendance Report',
    'logistics_report' => 'Logistics Report',
    'financial_report' => 'Financial Report',

    // Permissions
    'view_mobilizations' => 'View Mobilizations',
    'create_mobilizations' => 'Create Mobilizations',
    'update_mobilizations' => 'Update Mobilizations',
    'delete_mobilizations' => 'Delete Mobilizations',
    'export_mobilizations' => 'Export Mobilizations',
    'manage_participants' => 'Manage Participants',
    'manage_logistics' => 'Manage Logistics',
];

<?php

return [
    'title' => 'People Management',
    'title_description' => 'Manage all information related to people registered in the system',

    // Navigation and menus
    'listado' => 'Listing',
    'agregar_persona' => 'Add Person',
    'busqueda_avanzada' => 'Advanced Search',
    'detalles_persona' => 'Person Details',
    'editar_persona' => 'Edit Person',

    // Tabs
    'todos' => 'All',
    'militantes' => 'Militants',
    'votantes' => 'Voters',
    'simpatizantes' => 'Sympathizers',
    'lideres_1x10' => '1x10 Leaders',

    // Form fields
    'datos_personales' => 'Personal Data',
    'nombres' => 'First Names',
    'apellidos' => 'Last Names',
    'cedula' => 'ID Number',
    'fecha_nacimiento' => 'Birth Date',
    'genero' => 'Gender',
    'masculino' => 'Male',
    'femenino' => 'Female',
    'otro' => 'Other',

    // Contact information
    'informacion_contacto' => 'Contact Information',
    'telefono' => 'Phone',
    'telefono_secundario' => 'Secondary Phone',
    'email' => 'Email',
    'direccion' => 'Address',

    // Location
    'ubicacion' => 'Location',
    'estado' => 'State',
    'municipio' => 'Municipality',
    'parroquia' => 'Parish',
    'seleccionar_estado' => 'Select State',
    'seleccionar_municipio' => 'Select Municipality',
    'seleccionar_parroquia' => 'Select Parish',

    // Electoral information
    'informacion_electoral' => 'Electoral Information',
    'centro_votacion' => 'Voting Center',
    'mesa_votacion' => 'Voting Table',
    'seleccionar_centro' => 'Select Voting Center',

    // System role
    'rol_sistema' => 'System Role',
    'tipo_persona' => 'Person Type',
    'militante' => 'Militant',
    'votante' => 'Voter',
    'simpatizante' => 'Sympathizer',
    'es_lider_1x10' => 'Is 1x10 Leader',
    'lider_asignado' => 'Assigned Leader',
    'seleccionar_lider' => 'Select Leader',

    // Status and observations
    'estado_persona' => 'Status',
    'activo' => 'Active',
    'inactivo' => 'Inactive',
    'suspendido' => 'Suspended',
    'observaciones' => 'Observations',

    // Actions
    'guardar' => 'Save',
    'actualizar' => 'Update',
    'cancelar' => 'Cancel',
    'eliminar' => 'Delete',
    'ver_detalles' => 'View Details',
    'editar' => 'Edit',
    'crear_usuario' => 'Create User',
    'asignar_lider' => 'Assign Leader',
    'exportar' => 'Export',
    'importar' => 'Import',

    // Filters and search
    'buscar' => 'Search',
    'filtrar_por' => 'Filter by',
    'limpiar_filtros' => 'Clear Filters',
    'buscar_por_nombre' => 'Search by name, ID or phone',
    'filtrar_por_ubicacion' => 'Filter by location',
    'filtrar_por_rol' => 'Filter by role',
    'filtrar_por_estado' => 'Filter by status',

    // Additional information
    'edad' => 'Age',
    'años' => 'years',
    'nombre_completo' => 'Full Name',
    'personas_asignadas' => 'Assigned People',
    'espacios_disponibles' => 'Available Spaces',
    'historial_participacion' => 'Participation History',
    'eventos_electorales' => 'Electoral Events',
    'movilizaciones' => 'Mobilizations',

    // Messages
    'persona_creada' => 'Person created successfully',
    'persona_actualizada' => 'Person updated successfully',
    'persona_eliminada' => 'Person deleted successfully',
    'error_crear_persona' => 'Error creating person',
    'error_actualizar_persona' => 'Error updating person',
    'error_eliminar_persona' => 'Error deleting person',
    'cedula_ya_existe' => 'A person with this ID number already exists',
    'email_ya_existe' => 'A person with this email already exists',
    'no_resultados' => 'No people found with applied filters',

    // Confirmations
    'confirmar_eliminar' => 'Confirm Deletion',
    'seguro_eliminar' => 'Are you sure you want to delete this person?',
    'accion_irreversible' => 'This action cannot be undone.',
    'confirmar_crear_usuario' => 'Confirm User Creation',
    'crear_usuario_persona' => 'Do you want to create a system user for this person?',
    'credenciales_enviadas' => 'Credentials will be sent by email.',

    // Validation
    'campo_requerido' => 'This field is required',
    'email_invalido' => 'Please enter a valid email address',
    'telefono_invalido' => 'Please enter a valid phone number',
    'cedula_invalida' => 'Please enter a valid ID number',
    'edad_minima' => 'Person must be at least 18 years old',
];

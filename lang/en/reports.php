<?php

return [
    'title' => 'Reports',
    'title_description' => 'Generation and management of electoral system reports',

    // Navigation and menus
    'listing' => 'Reports Listing',
    'create_report' => 'Create Report',
    'report_details' => 'Report Details',
    'edit_report' => 'Edit Report',
    'report_history' => 'Report History',
    'scheduled_reports' => 'Scheduled Reports',

    // Report types
    'report_types' => 'Report Types',
    'people_report' => 'People Report',
    'voting_centers_report' => 'Voting Centers Report',
    'locations_report' => 'Locations Report',
    'electoral_events_report' => 'Electoral Events Report',
    'mobilizations_report' => 'Mobilizations Report',
    'statistical_report' => 'Statistical Report',
    'demographic_report' => 'Demographic Report',
    'participation_report' => 'Participation Report',
    'financial_report' => 'Financial Report',
    'custom_report' => 'Custom Report',

    // Basic report information
    'report_name' => 'Report Name',
    'report_description' => 'Report Description',
    'report_type' => 'Report Type',
    'report_category' => 'Report Category',
    'report_format' => 'Report Format',
    'report_period' => 'Report Period',
    'generation_date' => 'Generation Date',
    'report_status' => 'Report Status',

    // Report formats
    'formats' => 'Formats',
    'pdf_format' => 'PDF',
    'excel_format' => 'Excel',
    'csv_format' => 'CSV',
    'html_format' => 'HTML',
    'json_format' => 'JSON',
    'xml_format' => 'XML',

    // Report status
    'status' => 'Status',
    'draft' => 'Draft',
    'generating' => 'Generating',
    'completed' => 'Completed',
    'failed' => 'Failed',
    'scheduled' => 'Scheduled',
    'cancelled' => 'Cancelled',

    // Report configuration
    'report_config' => 'Report Configuration',
    'data_source' => 'Data Source',
    'filters' => 'Filters',
    'date_range' => 'Date Range',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'include_fields' => 'Fields to Include',
    'exclude_fields' => 'Fields to Exclude',
    'sort_by' => 'Sort by',
    'group_by' => 'Group by',

    // Specific filters
    'location_filters' => 'Location Filters',
    'state_filter' => 'Filter by State',
    'municipality_filter' => 'Filter by Municipality',
    'parish_filter' => 'Filter by Parish',
    'person_type_filter' => 'Filter by Person Type',
    'status_filter' => 'Filter by Status',
    'date_filter' => 'Filter by Date',
    'custom_filters' => 'Custom Filters',

    // Available fields
    'available_fields' => 'Available Fields',
    'selected_fields' => 'Selected Fields',
    'personal_data' => 'Personal Data',
    'contact_info' => 'Contact Information',
    'location_data' => 'Location Data',
    'electoral_data' => 'Electoral Data',
    'participation_data' => 'Participation Data',
    'statistical_data' => 'Statistical Data',

    // Report scheduling
    'schedule_report' => 'Schedule Report',
    'schedule_frequency' => 'Schedule Frequency',
    'daily' => 'Daily',
    'weekly' => 'Weekly',
    'monthly' => 'Monthly',
    'quarterly' => 'Quarterly',
    'yearly' => 'Yearly',
    'custom_schedule' => 'Custom Schedule',
    'next_execution' => 'Next Execution',
    'last_execution' => 'Last Execution',

    // Report distribution
    'distribution' => 'Distribution',
    'email_recipients' => 'Email Recipients',
    'auto_send' => 'Auto Send',
    'download_link' => 'Download Link',
    'notification_settings' => 'Notification Settings',
    'send_on_completion' => 'Send on Completion',
    'send_on_failure' => 'Send on Failure',

    // Actions
    'generate' => 'Generate',
    'download' => 'Download',
    'preview' => 'Preview',
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'duplicate' => 'Duplicate',
    'export' => 'Export',
    'print' => 'Print',
    'share' => 'Share',
    'schedule' => 'Schedule',

    // Report statistics
    'report_stats' => 'Report Statistics',
    'total_records' => 'Total Records',
    'filtered_records' => 'Filtered Records',
    'generation_time' => 'Generation Time',
    'file_size' => 'File Size',
    'download_count' => 'Download Count',
    'last_downloaded' => 'Last Downloaded',

    // Messages
    'report_generated' => 'Report generated successfully',
    'report_updated' => 'Report updated successfully',
    'report_deleted' => 'Report deleted successfully',
    'report_scheduled' => 'Report scheduled successfully',
    'report_sent' => 'Report sent successfully',
    'error_generating_report' => 'Error generating report',
    'error_updating_report' => 'Error updating report',
    'error_deleting_report' => 'Error deleting report',
    'no_data_found' => 'No data found for the report',
    'report_too_large' => 'Report is too large',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'confirm_delete_report' => 'Are you sure you want to delete this report?',
    'confirm_cancel_generation' => 'Are you sure you want to cancel the generation?',
    'action_irreversible' => 'This action cannot be undone.',

    // Validations
    'report_name_required' => 'Report name is required',
    'report_type_required' => 'Report type is required',
    'date_range_required' => 'Date range is required',
    'start_date_required' => 'Start date is required',
    'end_date_required' => 'End date is required',
    'end_date_after_start' => 'End date must be after start date',
    'at_least_one_field' => 'At least one field must be selected',
    'valid_email_required' => 'Valid email address is required',

    // Report templates
    'report_templates' => 'Report Templates',
    'use_template' => 'Use Template',
    'save_as_template' => 'Save as Template',
    'template_name' => 'Template Name',
    'template_description' => 'Template Description',
    'default_template' => 'Default Template',
    'custom_template' => 'Custom Template',

    // Advanced settings
    'advanced_settings' => 'Advanced Settings',
    'data_aggregation' => 'Data Aggregation',
    'chart_settings' => 'Chart Settings',
    'formatting_options' => 'Formatting Options',
    'header_footer' => 'Header and Footer',
    'watermark' => 'Watermark',
    'page_orientation' => 'Page Orientation',
    'page_size' => 'Page Size',

    // Analysis and metrics
    'analysis' => 'Analysis',
    'trends' => 'Trends',
    'comparisons' => 'Comparisons',
    'projections' => 'Projections',
    'correlations' => 'Correlations',
    'performance_indicators' => 'Performance Indicators',
    'key_metrics' => 'Key Metrics',

    // Additional information
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated',
    'created_by' => 'Created by',
    'updated_by' => 'Updated by',
    'file_path' => 'File Path',
    'file_name' => 'File Name',

    // Permissions
    'view_reports' => 'View Reports',
    'create_reports' => 'Create Reports',
    'update_reports' => 'Update Reports',
    'delete_reports' => 'Delete Reports',
    'export_reports' => 'Export Reports',
    'schedule_reports' => 'Schedule Reports',
    'share_reports' => 'Share Reports',
];

<?php

return [
    'title' => '1x10 Management',
    'title_description' => 'Manage the 1x10 methodology for electoral organization',

    // Navigation and menus
    'listado' => 'Listing',
    'busqueda_avanzada' => 'Advanced Search',
    'add_person' => 'Add Person',
    'person_details' => 'Person Details',
    'edit_person' => 'Edit Person',
    'manage_leaders' => 'Manage Leaders',

    // Tabs and categories
    'all' => 'All',
    'leaders' => 'Leaders',
    'assigned' => 'Assigned',
    'unassigned' => 'Unassigned',
    'active' => 'Active',
    'inactive' => 'Inactive',

    // 1x10 specific terms
    'leader_1x10' => '1x10 Leader',
    'leaders_1x10' => '1x10 Leaders',
    'assigned_people' => 'Assigned People',
    'available_spaces' => 'Available Spaces',
    'assign_leader' => 'Assign Leader',
    'remove_assignment' => 'Remove Assignment',
    'leader_capacity' => 'Leader Capacity',
    'full_capacity' => 'Full Capacity',

    // Form fields
    'personal_data' => 'Personal Data',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'full_name' => 'Full Name',
    'document_number' => 'Document Number',
    'birth_date' => 'Birth Date',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'other' => 'Other',

    // Contact information
    'contact_info' => 'Contact Information',
    'phone' => 'Phone',
    'secondary_phone' => 'Secondary Phone',
    'email' => 'Email',
    'address' => 'Address',

    // Location
    'location' => 'Location',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'voting_center' => 'Voting Center',
    'voting_table' => 'Voting Table',

    // Electoral information
    'electoral_info' => 'Electoral Information',
    'voter_id' => 'Voter ID',
    'voting_center_code' => 'Voting Center Code',
    'table_number' => 'Table Number',

    // Person types and roles
    'person_type' => 'Person Type',
    'militant' => 'Militant',
    'voter' => 'Voter',
    'sympathizer' => 'Sympathizer',
    'is_leader' => 'Is Leader',
    'assigned_leader' => 'Assigned Leader',
    'leadership_role' => 'Leadership Role',

    // Status
    'status' => 'Status',
    'active_status' => 'Active',
    'inactive_status' => 'Inactive',
    'suspended' => 'Suspended',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'create_user' => 'Create User',
    'export' => 'Export',
    'import' => 'Import',

    // Search and filters
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name, document or phone',
    'filter_by_location' => 'Filter by location',
    'filter_by_role' => 'Filter by role',
    'filter_by_status' => 'Filter by status',
    'filter_by_leadership' => 'Filter by leadership',
    'only_leaders' => 'Only leaders',
    'only_assigned' => 'Only assigned',

    // Statistics
    'total_people' => 'Total People',
    'total_leaders' => 'Total Leaders',
    'total_assigned' => 'Total Assigned',
    'available_leaders' => 'Available Leaders',
    'coverage_percentage' => 'Coverage Percentage',

    // Messages
    'person_created' => 'Person created successfully',
    'person_updated' => 'Person updated successfully',
    'person_deleted' => 'Person deleted successfully',
    'leader_assigned' => 'Leader assigned successfully',
    'assignment_removed' => 'Assignment removed successfully',
    'user_created' => 'User created successfully for the person',
    'error_creating_person' => 'Error creating person',
    'error_updating_person' => 'Error updating person',
    'error_deleting_person' => 'Error deleting person',
    'document_already_exists' => 'A person with this document number already exists',
    'email_already_exists' => 'A person with this email already exists',
    'leader_no_capacity' => 'The selected leader has no available capacity',
    'cannot_be_own_leader' => 'A person cannot be their own leader',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'are_you_sure_delete' => 'Are you sure you want to delete this person?',
    'this_action_cannot_be_undone' => 'This action cannot be undone.',
    'confirm_create_user' => 'Confirm User Creation',
    'create_user_for_person' => 'Do you want to create a system user for this person?',
    'credentials_will_be_sent' => 'Credentials will be sent by email.',

    // Export
    'export_excel' => 'Export to Excel',
    'export_pdf' => 'Export to PDF',
    'export_csv' => 'Export to CSV',
    'select_fields' => 'Select Fields to Export',
    'export_successful' => 'Export completed successfully',

    // Validation
    'required_field' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'invalid_phone' => 'Please enter a valid phone number',
    'invalid_document' => 'Please enter a valid document number',
    'min_age' => 'Person must be at least 18 years old',
];

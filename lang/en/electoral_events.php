<?php

return [
    'title' => 'Electoral Events',
    'title_description' => 'Management of elections, referendums and other electoral events',

    // Navigation and menus
    'listing' => 'Listing',
    'add_event' => 'Add Event',
    'event_details' => 'Event Details',
    'edit_event' => 'Edit Event',
    'manage_candidates' => 'Manage Candidates',
    'manage_results' => 'Manage Results',

    // Event types
    'event_type' => 'Event Type',
    'presidential' => 'Presidential',
    'parliamentary' => 'Parliamentary',
    'regional' => 'Regional',
    'municipal' => 'Municipal',
    'referendum' => 'Referendum',
    'consultation' => 'Popular Consultation',
    'primary' => 'Primary',
    'other' => 'Other',

    // Basic information
    'event_name' => 'Event Name',
    'event_code' => 'Event Code',
    'description' => 'Description',
    'election_date' => 'Election Date',
    'registration_start' => 'Registration Start',
    'registration_end' => 'Registration End',
    'campaign_start' => 'Campaign Start',
    'campaign_end' => 'Campaign End',

    // Event status
    'status' => 'Status',
    'planned' => 'Planned',
    'registration_open' => 'Registration Open',
    'campaign_period' => 'Campaign Period',
    'voting_day' => 'Voting Day',
    'counting' => 'Counting',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'suspended' => 'Suspended',

    // Electoral configuration
    'electoral_config' => 'Electoral Configuration',
    'voting_system' => 'Voting System',
    'ballot_type' => 'Ballot Type',
    'voting_hours' => 'Voting Hours',
    'start_time' => 'Start Time',
    'end_time' => 'End Time',
    'eligible_voters' => 'Eligible Voters',
    'total_centers' => 'Total Centers',
    'total_tables' => 'Total Tables',

    // Candidates and parties
    'candidates' => 'Candidates',
    'parties' => 'Parties',
    'candidate_name' => 'Candidate Name',
    'party_name' => 'Party Name',
    'coalition' => 'Coalition',
    'ballot_position' => 'Ballot Position',
    'add_candidate' => 'Add Candidate',
    'edit_candidate' => 'Edit Candidate',
    'remove_candidate' => 'Remove Candidate',

    // Results
    'results' => 'Results',
    'preliminary_results' => 'Preliminary Results',
    'final_results' => 'Final Results',
    'votes_count' => 'Vote Count',
    'total_votes' => 'Total Votes',
    'valid_votes' => 'Valid Votes',
    'invalid_votes' => 'Invalid Votes',
    'blank_votes' => 'Blank Votes',
    'null_votes' => 'Null Votes',
    'participation' => 'Participation',
    'abstention' => 'Abstention',

    // Geographic scope
    'geographic_scope' => 'Geographic Scope',
    'national' => 'National',
    'state_level' => 'State Level',
    'municipal_level' => 'Municipal Level',
    'parish_level' => 'Parish Level',
    'affected_states' => 'Affected States',
    'affected_municipalities' => 'Affected Municipalities',
    'affected_parishes' => 'Affected Parishes',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'publish' => 'Publish',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'export' => 'Export',
    'import' => 'Import',
    'generate_report' => 'Generate Report',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name or code',
    'filter_by_type' => 'Filter by type',
    'filter_by_status' => 'Filter by status',
    'filter_by_date' => 'Filter by date',
    'filter_by_scope' => 'Filter by scope',
    'advanced_search' => 'Advanced Search',

    // Statistics
    'statistics' => 'Statistics',
    'total_events' => 'Total Events',
    'active_events' => 'Active Events',
    'completed_events' => 'Completed Events',
    'upcoming_events' => 'Upcoming Events',
    'events_by_type' => 'Events by Type',
    'events_by_year' => 'Events by Year',
    'average_participation' => 'Average Participation',

    // Messages
    'event_created' => 'Electoral event created successfully',
    'event_updated' => 'Electoral event updated successfully',
    'event_deleted' => 'Electoral event deleted successfully',
    'candidate_added' => 'Candidate added successfully',
    'candidate_updated' => 'Candidate updated successfully',
    'candidate_removed' => 'Candidate removed successfully',
    'results_updated' => 'Results updated successfully',
    'error_creating_event' => 'Error creating electoral event',
    'error_updating_event' => 'Error updating electoral event',
    'error_deleting_event' => 'Error deleting electoral event',
    'event_code_exists' => 'An event with this code already exists',
    'no_results' => 'No events found with applied filters',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'confirm_delete_event' => 'Are you sure you want to delete this electoral event?',
    'confirm_delete_candidate' => 'Are you sure you want to remove this candidate?',
    'action_irreversible' => 'This action cannot be undone.',
    'event_has_results' => 'This event has registered results.',
    'event_is_active' => 'This event is active.',

    // Validations
    'event_name_required' => 'Event name is required',
    'event_code_required' => 'Event code is required',
    'event_type_required' => 'Event type is required',
    'election_date_required' => 'Election date is required',
    'election_date_future' => 'Election date must be in the future',
    'registration_dates_valid' => 'Registration dates must be valid',
    'campaign_dates_valid' => 'Campaign dates must be valid',
    'voting_hours_valid' => 'Voting hours must be valid',
    'candidate_name_required' => 'Candidate name is required',
    'party_name_required' => 'Party name is required',

    // Export and import
    'export_excel' => 'Export to Excel',
    'export_pdf' => 'Export to PDF',
    'export_csv' => 'Export to CSV',
    'import_events' => 'Import Events',
    'import_candidates' => 'Import Candidates',
    'import_results' => 'Import Results',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    'file_formats' => 'Allowed formats: Excel (.xlsx), CSV (.csv)',
    'export_successful' => 'Export completed successfully',
    'import_successful' => 'Import completed successfully',
    'import_errors' => 'Errors found during import',

    // Additional information
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated',
    'created_by' => 'Created by',
    'updated_by' => 'Updated by',
    'legal_framework' => 'Legal Framework',
    'regulations' => 'Regulations',
    'budget' => 'Budget',
    'cost' => 'Cost',

    // Reports
    'reports' => 'Reports',
    'event_report' => 'Event Report',
    'candidates_report' => 'Candidates Report',
    'results_report' => 'Results Report',
    'participation_report' => 'Participation Report',
    'statistical_report' => 'Statistical Report',

    // Permissions
    'view_electoral_events' => 'View Electoral Events',
    'create_electoral_events' => 'Create Electoral Events',
    'update_electoral_events' => 'Update Electoral Events',
    'delete_electoral_events' => 'Delete Electoral Events',
    'export_electoral_events' => 'Export Electoral Events',
    'manage_candidates' => 'Manage Candidates',
    'manage_results' => 'Manage Results',
];

<?php

return [
    'title' => 'Geographic Locations',
    'title_description' => 'Management of states, municipalities and parishes of Venezuela',

    // Navigation and menus
    'listing' => 'Listing',
    'add_location' => 'Add Location',
    'location_details' => 'Location Details',
    'edit_location' => 'Edit Location',
    'manage_hierarchy' => 'Manage Hierarchy',

    // Location types
    'states' => 'States',
    'municipalities' => 'Municipalities',
    'parishes' => 'Parishes',
    'state' => 'State',
    'municipality' => 'Municipality',
    'parish' => 'Parish',
    'location_type' => 'Location Type',

    // Basic information
    'location_code' => 'Code',
    'location_name' => 'Name',
    'official_name' => 'Official Name',
    'short_name' => 'Short Name',
    'capital' => 'Capital',
    'area' => 'Area (km²)',
    'population' => 'Population',
    'postal_code' => 'Postal Code',

    // Geographic hierarchy
    'parent_location' => 'Parent Location',
    'child_locations' => 'Child Locations',
    'belongs_to_state' => 'Belongs to State',
    'belongs_to_municipality' => 'Belongs to Municipality',
    'select_state' => 'Select State',
    'select_municipality' => 'Select Municipality',
    'select_parish' => 'Select Parish',

    // Location status
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',

    // Geographic information
    'geographic_info' => 'Geographic Information',
    'coordinates' => 'Coordinates',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',
    'altitude' => 'Altitude (masl)',
    'timezone' => 'Timezone',
    'climate' => 'Climate',

    // Demographic information
    'demographic_info' => 'Demographic Information',
    'total_population' => 'Total Population',
    'urban_population' => 'Urban Population',
    'rural_population' => 'Rural Population',
    'population_density' => 'Population Density',
    'households' => 'Households',

    // Electoral information
    'electoral_info' => 'Electoral Information',
    'voting_centers' => 'Voting Centers',
    'voting_tables' => 'Voting Tables',
    'registered_voters' => 'Registered Voters',
    'electoral_circuits' => 'Electoral Circuits',

    // Actions
    'save' => 'Save',
    'update' => 'Update',
    'cancel' => 'Cancel',
    'delete' => 'Delete',
    'view_details' => 'View Details',
    'edit' => 'Edit',
    'export' => 'Export',
    'import' => 'Import',
    'view_map' => 'View on Map',
    'download_data' => 'Download Data',

    // Filters and search
    'search' => 'Search',
    'filter_by' => 'Filter by',
    'clear_filters' => 'Clear Filters',
    'search_by_name' => 'Search by name or code',
    'filter_by_type' => 'Filter by type',
    'filter_by_state' => 'Filter by state',
    'filter_by_status' => 'Filter by status',
    'advanced_search' => 'Advanced Search',

    // Statistics
    'statistics' => 'Statistics',
    'total_states' => 'Total States',
    'total_municipalities' => 'Total Municipalities',
    'total_parishes' => 'Total Parishes',
    'total_population_country' => 'Total Country Population',
    'largest_state' => 'Largest State',
    'smallest_state' => 'Smallest State',
    'most_populated' => 'Most Populated',
    'least_populated' => 'Least Populated',

    // Messages
    'location_created' => 'Location created successfully',
    'location_updated' => 'Location updated successfully',
    'location_deleted' => 'Location deleted successfully',
    'error_creating_location' => 'Error creating location',
    'error_updating_location' => 'Error updating location',
    'error_deleting_location' => 'Error deleting location',
    'location_code_exists' => 'A location with this code already exists',
    'location_name_exists' => 'A location with this name already exists',
    'no_results' => 'No locations found with applied filters',

    // Confirmations
    'confirm_delete' => 'Confirm Deletion',
    'confirm_delete_location' => 'Are you sure you want to delete this location?',
    'action_irreversible' => 'This action cannot be undone.',
    'location_has_children' => 'This location has dependent locations.',
    'location_has_centers' => 'This location has associated voting centers.',
    'location_has_people' => 'This location has registered people.',

    // Validations
    'location_code_required' => 'Location code is required',
    'location_name_required' => 'Location name is required',
    'location_type_required' => 'Location type is required',
    'parent_location_required' => 'Parent location must be selected',
    'coordinates_invalid' => 'Coordinates are not valid',
    'population_numeric' => 'Population must be a number',
    'area_numeric' => 'Area must be a number',

    // Export and import
    'export_excel' => 'Export to Excel',
    'export_pdf' => 'Export to PDF',
    'export_csv' => 'Export to CSV',
    'import_locations' => 'Import Locations',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    'file_formats' => 'Allowed formats: Excel (.xlsx), CSV (.csv)',
    'export_successful' => 'Export completed successfully',
    'import_successful' => 'Import completed successfully',
    'import_errors' => 'Errors found during import',

    // Additional information
    'notes' => 'Notes',
    'created_at' => 'Created At',
    'updated_at' => 'Last Updated',
    'created_by' => 'Created by',
    'updated_by' => 'Updated by',
    'historical_data' => 'Historical Data',
    'economic_data' => 'Economic Data',

    // Reports
    'reports' => 'Reports',
    'demographic_report' => 'Demographic Report',
    'electoral_report' => 'Electoral Report',
    'geographic_report' => 'Geographic Report',
    'hierarchy_report' => 'Hierarchy Report',
    'generate_report' => 'Generate Report',

    // Venezuelan states
    'venezuela_states' => [
        'amazonas' => 'Amazonas',
        'anzoategui' => 'Anzoátegui',
        'apure' => 'Apure',
        'aragua' => 'Aragua',
        'barinas' => 'Barinas',
        'bolivar' => 'Bolívar',
        'carabobo' => 'Carabobo',
        'cojedes' => 'Cojedes',
        'delta_amacuro' => 'Delta Amacuro',
        'distrito_capital' => 'Capital District',
        'falcon' => 'Falcón',
        'guarico' => 'Guárico',
        'lara' => 'Lara',
        'merida' => 'Mérida',
        'miranda' => 'Miranda',
        'monagas' => 'Monagas',
        'nueva_esparta' => 'Nueva Esparta',
        'portuguesa' => 'Portuguesa',
        'sucre' => 'Sucre',
        'tachira' => 'Táchira',
        'trujillo' => 'Trujillo',
        'vargas' => 'Vargas',
        'yaracuy' => 'Yaracuy',
        'zulia' => 'Zulia',
    ],

    // Permissions
    'view_locations' => 'View Locations',
    'create_locations' => 'Create Locations',
    'update_locations' => 'Update Locations',
    'delete_locations' => 'Delete Locations',
    'export_locations' => 'Export Locations',
    'import_locations' => 'Import Locations',
];

<?php

return [
    'title' => 'Dashboard',
    'title_description' => 'General overview of the electoral system',
    'welcome' => 'Welcome to the Electoral System',
    'welcome_message' => 'Manage all electoral information from this central panel',

    // Main widgets
    'overview' => 'Overview',
    'quick_stats' => 'Quick Statistics',
    'recent_activity' => 'Recent Activity',
    'notifications' => 'Notifications',
    'alerts' => 'Alerts',
    'shortcuts' => 'Quick Access',

    // People statistics
    'people_stats' => 'People Statistics',
    'total_people' => 'Total People',
    'total_militants' => 'Total Militants',
    'total_voters' => 'Total Voters',
    'total_sympathizers' => 'Total Sympathizers',
    'total_leaders' => 'Total 1x10 Leaders',
    'active_people' => 'Active People',
    'inactive_people' => 'Inactive People',
    'people_without_leader' => 'People without Leader',
    'leaders_with_spaces' => 'Leaders with Available Spaces',

    // Electoral statistics
    'electoral_stats' => 'Electoral Statistics',
    'total_voting_centers' => 'Total Voting Centers',
    'active_voting_centers' => 'Active Centers',
    'inactive_voting_centers' => 'Inactive Centers',
    'total_voting_tables' => 'Total Voting Tables',
    'total_registered_voters' => 'Total Registered Voters',
    'electoral_capacity' => 'Electoral Capacity',
    'centers_by_state' => 'Centers by State',

    // Location statistics
    'location_stats' => 'Location Statistics',
    'total_states' => 'Total States',
    'total_municipalities' => 'Total Municipalities',
    'total_parishes' => 'Total Parishes',
    'coverage_percentage' => 'Coverage Percentage',

    // Event statistics
    'events_stats' => 'Event Statistics',
    'total_electoral_events' => 'Total Electoral Events',
    'active_events' => 'Active Events',
    'upcoming_events' => 'Upcoming Events',
    'completed_events' => 'Completed Events',
    'events_this_month' => 'Events this Month',
    'events_this_year' => 'Events this Year',

    // Mobilization statistics
    'mobilizations_stats' => 'Mobilization Statistics',
    'total_mobilizations' => 'Total Mobilizations',
    'active_mobilizations' => 'Active Mobilizations',
    'upcoming_mobilizations' => 'Upcoming Mobilizations',
    'completed_mobilizations' => 'Completed Mobilizations',
    'total_participants' => 'Total Participants',
    'average_attendance' => 'Average Attendance',

    // Charts and visualizations
    'charts' => 'Charts',
    'people_by_type' => 'People by Type',
    'people_by_state' => 'People by State',
    'centers_by_type' => 'Centers by Type',
    'events_by_month' => 'Events by Month',
    'mobilizations_by_type' => 'Mobilizations by Type',
    'growth_trend' => 'Growth Trend',
    'participation_trend' => 'Participation Trend',

    // Recent activity
    'recent_registrations' => 'Recent Registrations',
    'recent_updates' => 'Recent Updates',
    'recent_events' => 'Recent Events',
    'recent_mobilizations' => 'Recent Mobilizations',
    'last_login' => 'Last Login',
    'system_activity' => 'System Activity',

    // Notifications and alerts
    'pending_approvals' => 'Pending Approvals',
    'system_alerts' => 'System Alerts',
    'low_capacity_centers' => 'Low Capacity Centers',
    'incomplete_profiles' => 'Incomplete Profiles',
    'expired_events' => 'Expired Events',
    'overdue_tasks' => 'Overdue Tasks',
    'maintenance_required' => 'Maintenance Required',

    // Quick access
    'quick_actions' => 'Quick Actions',
    'add_person' => 'Add Person',
    'add_voting_center' => 'Add Voting Center',
    'add_event' => 'Add Event',
    'add_mobilization' => 'Add Mobilization',
    'generate_report' => 'Generate Report',
    'export_data' => 'Export Data',
    'import_data' => 'Import Data',
    'system_settings' => 'System Settings',

    // Time filters
    'time_filters' => 'Time Filters',
    'today' => 'Today',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'this_quarter' => 'This Quarter',
    'this_year' => 'This Year',
    'last_7_days' => 'Last 7 Days',
    'last_30_days' => 'Last 30 Days',
    'last_90_days' => 'Last 90 Days',
    'custom_range' => 'Custom Range',

    // Status and indicators
    'status_indicators' => 'Status Indicators',
    'system_health' => 'System Health',
    'database_status' => 'Database Status',
    'server_status' => 'Server Status',
    'backup_status' => 'Backup Status',
    'online' => 'Online',
    'offline' => 'Offline',
    'healthy' => 'Healthy',
    'warning' => 'Warning',
    'critical' => 'Critical',

    // User information
    'user_info' => 'User Information',
    'current_user' => 'Current User',
    'user_role' => 'User Role',
    'last_activity' => 'Last Activity',
    'session_duration' => 'Session Duration',
    'permissions_summary' => 'Permissions Summary',

    // Performance metrics
    'performance_metrics' => 'Performance Metrics',
    'response_time' => 'Response Time',
    'page_load_time' => 'Page Load Time',
    'database_queries' => 'Database Queries',
    'memory_usage' => 'Memory Usage',
    'cpu_usage' => 'CPU Usage',
    'disk_usage' => 'Disk Usage',

    // Temporal comparisons
    'compared_to_last_month' => 'Compared to last month',
    'compared_to_last_week' => 'Compared to last week',
    'compared_to_yesterday' => 'Compared to yesterday',
    'increase' => 'Increase',
    'decrease' => 'Decrease',
    'no_change' => 'No Change',
    'percentage_change' => 'Percentage Change',

    // System messages
    'loading' => 'Loading...',
    'no_data' => 'No data available',
    'error_loading_data' => 'Error loading data',
    'refresh_data' => 'Refresh Data',
    'last_updated' => 'Last Updated',
    'auto_refresh' => 'Auto Refresh',
    'manual_refresh' => 'Manual Refresh',

    // Dashboard configuration
    'dashboard_settings' => 'Dashboard Settings',
    'customize_dashboard' => 'Customize Dashboard',
    'widget_settings' => 'Widget Settings',
    'layout_options' => 'Layout Options',
    'theme_settings' => 'Theme Settings',
    'notification_settings' => 'Notification Settings',

    // Export and reports
    'export_dashboard' => 'Export Dashboard',
    'dashboard_report' => 'Dashboard Report',
    'summary_report' => 'Summary Report',
    'detailed_report' => 'Detailed Report',
    'print_dashboard' => 'Print Dashboard',
    'save_as_pdf' => 'Save as PDF',

    // Help and support
    'help' => 'Help',
    'user_guide' => 'User Guide',
    'system_documentation' => 'System Documentation',
    'contact_support' => 'Contact Support',
    'report_issue' => 'Report Issue',
    'feedback' => 'Feedback',

    // Permissions
    'access_dashboard' => 'Access Dashboard',
    'view_dashboard_stats' => 'View Dashboard Statistics',
    'customize_dashboard' => 'Customize Dashboard',
    'export_dashboard_data' => 'Export Dashboard Data',
];

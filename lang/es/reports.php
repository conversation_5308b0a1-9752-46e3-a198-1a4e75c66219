<?php

return [
    'title' => 'Reportes',
    'title_description' => 'Generación y gestión de reportes del sistema electoral',

    // Navegación y menús
    'listing' => 'Listado de Reportes',
    'create_report' => 'Crear Reporte',
    'report_details' => 'Detalles del Reporte',
    'edit_report' => 'Editar Reporte',
    'report_history' => 'Historial de Reportes',
    'scheduled_reports' => 'Reportes Programados',

    // Tipos de reportes
    'report_types' => 'Tipos de Reportes',
    'people_report' => 'Reporte de Personas',
    'voting_centers_report' => 'Reporte de Centros de Votación',
    'locations_report' => 'Reporte de Ubicaciones',
    'electoral_events_report' => 'Reporte de Eventos Electorales',
    'mobilizations_report' => 'Reporte de Movilizaciones',
    'statistical_report' => 'Reporte Estadístico',
    'demographic_report' => 'Reporte Demográfico',
    'participation_report' => 'Reporte de Participación',
    'financial_report' => 'Reporte Financiero',
    'custom_report' => 'Reporte Personalizado',

    // Información básica del reporte
    'report_name' => 'Nombre del Reporte',
    'report_description' => 'Descripción del Reporte',
    'report_type' => 'Tipo de Reporte',
    'report_category' => 'Categoría del Reporte',
    'report_format' => 'Formato del Reporte',
    'report_period' => 'Período del Reporte',
    'generation_date' => 'Fecha de Generación',
    'report_status' => 'Estado del Reporte',

    // Formatos de reporte
    'formats' => 'Formatos',
    'pdf_format' => 'PDF',
    'excel_format' => 'Excel',
    'csv_format' => 'CSV',
    'html_format' => 'HTML',
    'json_format' => 'JSON',
    'xml_format' => 'XML',

    // Estados del reporte
    'status' => 'Estado',
    'draft' => 'Borrador',
    'generating' => 'Generando',
    'completed' => 'Completado',
    'failed' => 'Fallido',
    'scheduled' => 'Programado',
    'cancelled' => 'Cancelado',

    // Configuración del reporte
    'report_config' => 'Configuración del Reporte',
    'data_source' => 'Fuente de Datos',
    'filters' => 'Filtros',
    'date_range' => 'Rango de Fechas',
    'start_date' => 'Fecha de Inicio',
    'end_date' => 'Fecha de Fin',
    'include_fields' => 'Campos a Incluir',
    'exclude_fields' => 'Campos a Excluir',
    'sort_by' => 'Ordenar por',
    'group_by' => 'Agrupar por',

    // Filtros específicos
    'location_filters' => 'Filtros de Ubicación',
    'state_filter' => 'Filtrar por Estado',
    'municipality_filter' => 'Filtrar por Municipio',
    'parish_filter' => 'Filtrar por Parroquia',
    'person_type_filter' => 'Filtrar por Tipo de Persona',
    'status_filter' => 'Filtrar por Estado',
    'date_filter' => 'Filtrar por Fecha',
    'custom_filters' => 'Filtros Personalizados',

    // Campos disponibles
    'available_fields' => 'Campos Disponibles',
    'selected_fields' => 'Campos Seleccionados',
    'personal_data' => 'Datos Personales',
    'contact_info' => 'Información de Contacto',
    'location_data' => 'Datos de Ubicación',
    'electoral_data' => 'Datos Electorales',
    'participation_data' => 'Datos de Participación',
    'statistical_data' => 'Datos Estadísticos',

    // Programación de reportes
    'schedule_report' => 'Programar Reporte',
    'schedule_frequency' => 'Frecuencia de Programación',
    'daily' => 'Diario',
    'weekly' => 'Semanal',
    'monthly' => 'Mensual',
    'quarterly' => 'Trimestral',
    'yearly' => 'Anual',
    'custom_schedule' => 'Programación Personalizada',
    'next_execution' => 'Próxima Ejecución',
    'last_execution' => 'Última Ejecución',

    // Distribución del reporte
    'distribution' => 'Distribución',
    'email_recipients' => 'Destinatarios de Correo',
    'auto_send' => 'Envío Automático',
    'download_link' => 'Enlace de Descarga',
    'notification_settings' => 'Configuración de Notificaciones',
    'send_on_completion' => 'Enviar al Completar',
    'send_on_failure' => 'Enviar en Caso de Error',

    // Acciones
    'generate' => 'Generar',
    'download' => 'Descargar',
    'preview' => 'Vista Previa',
    'save' => 'Guardar',
    'update' => 'Actualizar',
    'cancel' => 'Cancelar',
    'delete' => 'Eliminar',
    'duplicate' => 'Duplicar',
    'export' => 'Exportar',
    'print' => 'Imprimir',
    'share' => 'Compartir',
    'schedule' => 'Programar',

    // Estadísticas del reporte
    'report_stats' => 'Estadísticas del Reporte',
    'total_records' => 'Total de Registros',
    'filtered_records' => 'Registros Filtrados',
    'generation_time' => 'Tiempo de Generación',
    'file_size' => 'Tamaño del Archivo',
    'download_count' => 'Número de Descargas',
    'last_downloaded' => 'Última Descarga',

    // Mensajes
    'report_generated' => 'Reporte generado exitosamente',
    'report_updated' => 'Reporte actualizado exitosamente',
    'report_deleted' => 'Reporte eliminado exitosamente',
    'report_scheduled' => 'Reporte programado exitosamente',
    'report_sent' => 'Reporte enviado exitosamente',
    'error_generating_report' => 'Error al generar el reporte',
    'error_updating_report' => 'Error al actualizar el reporte',
    'error_deleting_report' => 'Error al eliminar el reporte',
    'no_data_found' => 'No se encontraron datos para el reporte',
    'report_too_large' => 'El reporte es demasiado grande',

    // Confirmaciones
    'confirm_delete' => 'Confirmar Eliminación',
    'confirm_delete_report' => '¿Está seguro de que desea eliminar este reporte?',
    'confirm_cancel_generation' => '¿Está seguro de que desea cancelar la generación?',
    'action_irreversible' => 'Esta acción no se puede deshacer.',

    // Validaciones
    'report_name_required' => 'El nombre del reporte es obligatorio',
    'report_type_required' => 'El tipo de reporte es obligatorio',
    'date_range_required' => 'El rango de fechas es obligatorio',
    'start_date_required' => 'La fecha de inicio es obligatoria',
    'end_date_required' => 'La fecha de fin es obligatoria',
    'end_date_after_start' => 'La fecha de fin debe ser posterior a la fecha de inicio',
    'at_least_one_field' => 'Debe seleccionar al menos un campo',
    'valid_email_required' => 'Se requiere un correo electrónico válido',

    // Plantillas de reporte
    'report_templates' => 'Plantillas de Reporte',
    'use_template' => 'Usar Plantilla',
    'save_as_template' => 'Guardar como Plantilla',
    'template_name' => 'Nombre de Plantilla',
    'template_description' => 'Descripción de Plantilla',
    'default_template' => 'Plantilla por Defecto',
    'custom_template' => 'Plantilla Personalizada',

    // Configuración avanzada
    'advanced_settings' => 'Configuración Avanzada',
    'data_aggregation' => 'Agregación de Datos',
    'chart_settings' => 'Configuración de Gráficos',
    'formatting_options' => 'Opciones de Formato',
    'header_footer' => 'Encabezado y Pie de Página',
    'watermark' => 'Marca de Agua',
    'page_orientation' => 'Orientación de Página',
    'page_size' => 'Tamaño de Página',

    // Análisis y métricas
    'analysis' => 'Análisis',
    'trends' => 'Tendencias',
    'comparisons' => 'Comparaciones',
    'projections' => 'Proyecciones',
    'correlations' => 'Correlaciones',
    'performance_indicators' => 'Indicadores de Rendimiento',
    'key_metrics' => 'Métricas Clave',

    // Información adicional
    'notes' => 'Observaciones',
    'created_at' => 'Fecha de Creación',
    'updated_at' => 'Última Actualización',
    'created_by' => 'Creado por',
    'updated_by' => 'Actualizado por',
    'file_path' => 'Ruta del Archivo',
    'file_name' => 'Nombre del Archivo',

    // Permisos
    'view_reports' => 'Ver Reportes',
    'create_reports' => 'Crear Reportes',
    'update_reports' => 'Actualizar Reportes',
    'delete_reports' => 'Eliminar Reportes',
    'export_reports' => 'Exportar Reportes',
    'schedule_reports' => 'Programar Reportes',
    'share_reports' => 'Compartir Reportes',
];

<?php

return [
    'title' => 'Gestión de Personas',
    'title_description' => 'Gestionar toda la información relacionada con las personas registradas en el sistema',

    // Navegación y menús
    'listado' => 'Listado',
    'agregar_persona' => 'Agregar Persona',
    'busqueda_avanzada' => 'Búsqueda Avanzada',
    'detalles_persona' => 'Detalles de Persona',
    'editar_persona' => 'Editar Persona',

    // Pestañas
    'todos' => 'Todos',
    'militantes' => 'Militantes',
    'votantes' => 'Votantes',
    'simpatizantes' => 'Simpatizantes',
    'lideres_1x10' => 'Líderes 1x10',

    // Campos del formulario
    'datos_personales' => 'Datos Personales',
    'nombres' => 'Nombres',
    'apellidos' => 'Apellidos',
    'cedula' => 'Cédula de Identidad',
    'fecha_nacimiento' => 'Fecha de Nacimiento',
    'genero' => '<PERSON><PERSON><PERSON>',
    'masculino' => 'Masculino',
    'femenino' => 'Fe<PERSON><PERSON>',
    'otro' => 'Otro',

    // Información de contacto
    'informacion_contacto' => 'Información de Contacto',
    'telefono' => 'Teléfono',
    'telefono_secundario' => 'Teléfono Secundario',
    'email' => 'Correo Electrónico',
    'direccion' => 'Dirección',

    // Ubicación
    'ubicacion' => 'Ubicación',
    'estado' => 'Estado',
    'municipio' => 'Municipio',
    'parroquia' => 'Parroquia',
    'seleccionar_estado' => 'Seleccionar Estado',
    'seleccionar_municipio' => 'Seleccionar Municipio',
    'seleccionar_parroquia' => 'Seleccionar Parroquia',

    // Información electoral
    'informacion_electoral' => 'Información Electoral',
    'centro_votacion' => 'Centro de Votación',
    'mesa_votacion' => 'Mesa de Votación',
    'seleccionar_centro' => 'Seleccionar Centro de Votación',

    // Rol en el sistema
    'rol_sistema' => 'Rol en el Sistema',
    'tipo_persona' => 'Tipo de Persona',
    'militante' => 'Militante',
    'votante' => 'Votante',
    'simpatizante' => 'Simpatizante',
    'es_lider_1x10' => 'Es Líder 1x10',
    'lider_asignado' => 'Líder Asignado',
    'seleccionar_lider' => 'Seleccionar Líder',

    // Estado y observaciones
    'estado_persona' => 'Estado',
    'activo' => 'Activo',
    'inactivo' => 'Inactivo',
    'suspendido' => 'Suspendido',
    'observaciones' => 'Observaciones',

    // Acciones
    'guardar' => 'Guardar',
    'actualizar' => 'Actualizar',
    'cancelar' => 'Cancelar',
    'eliminar' => 'Eliminar',
    'ver_detalles' => 'Ver Detalles',
    'editar' => 'Editar',
    'crear_usuario' => 'Crear Usuario',
    'asignar_lider' => 'Asignar Líder',
    'exportar' => 'Exportar',
    'importar' => 'Importar',

    // Filtros y búsqueda
    'buscar' => 'Buscar',
    'filtrar_por' => 'Filtrar por',
    'limpiar_filtros' => 'Limpiar Filtros',
    'buscar_por_nombre' => 'Buscar por nombre, cédula o teléfono',
    'filtrar_por_ubicacion' => 'Filtrar por ubicación',
    'filtrar_por_rol' => 'Filtrar por rol',
    'filtrar_por_estado' => 'Filtrar por estado',

    // Información adicional
    'edad' => 'Edad',
    'años' => 'años',
    'nombre_completo' => 'Nombre Completo',
    'personas_asignadas' => 'Personas Asignadas',
    'espacios_disponibles' => 'Espacios Disponibles',
    'historial_participacion' => 'Historial de Participación',
    'eventos_electorales' => 'Eventos Electorales',
    'movilizaciones' => 'Movilizaciones',

    // Mensajes
    'persona_creada' => 'Persona creada exitosamente',
    'persona_actualizada' => 'Persona actualizada exitosamente',
    'persona_eliminada' => 'Persona eliminada exitosamente',
    'error_crear_persona' => 'Error al crear la persona',
    'error_actualizar_persona' => 'Error al actualizar la persona',
    'error_eliminar_persona' => 'Error al eliminar la persona',
    'cedula_ya_existe' => 'Ya existe una persona con esta cédula de identidad',
    'email_ya_existe' => 'Ya existe una persona con este correo electrónico',
    'no_resultados' => 'No se encontraron personas con los filtros aplicados',

    // Confirmaciones
    'confirmar_eliminar' => 'Confirmar Eliminación',
    'seguro_eliminar' => '¿Está seguro de que desea eliminar esta persona?',
    'accion_irreversible' => 'Esta acción no se puede deshacer.',
    'confirmar_crear_usuario' => 'Confirmar Creación de Usuario',
    'crear_usuario_persona' => '¿Desea crear un usuario del sistema para esta persona?',
    'credenciales_enviadas' => 'Las credenciales serán enviadas por correo electrónico.',

    // Validación
    'campo_requerido' => 'Este campo es requerido',
    'email_invalido' => 'Por favor ingrese un correo electrónico válido',
    'telefono_invalido' => 'Por favor ingrese un número de teléfono válido',
    'cedula_invalida' => 'Por favor ingrese una cédula de identidad válida',
    'edad_minima' => 'La persona debe tener al menos 18 años',

    // Claves adicionales para breadcrumbs y navegación
    'nombre_completo' => 'Nombre Completo',
    'exportacion_en_desarrollo' => 'Funcionalidad de exportación en desarrollo',
    'se_enviaran_credenciales' => 'Las credenciales serán enviadas por correo electrónico',
    'persona_eliminada' => 'Persona eliminada exitosamente',
    'error_eliminar_persona' => 'Error al eliminar la persona',
];

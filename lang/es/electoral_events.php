<?php

return [
    'title' => 'Eventos Electorales',
    'title_description' => 'Gestión de elecciones, referendos y otros eventos electorales',

    // Navegación y menús
    'listing' => 'Listado',
    'add_event' => 'Agregar Evento',
    'event_details' => 'Detalles del Evento',
    'edit_event' => 'Editar Evento',
    'manage_candidates' => 'Gestionar Candidatos',
    'manage_results' => 'Gestionar Resultados',

    // Tipos de eventos
    'event_type' => 'Tipo de Evento',
    'presidential' => 'Presidencial',
    'parliamentary' => 'Parlamentaria',
    'regional' => 'Regional',
    'municipal' => 'Municipal',
    'referendum' => 'Referéndum',
    'consultation' => 'Consulta Popular',
    'primary' => 'Primaria',
    'other' => 'Otro',

    // Información básica
    'event_name' => 'Nombre del Evento',
    'event_code' => 'Código del Evento',
    'description' => 'Descripción',
    'election_date' => 'Fecha de Elección',
    'registration_start' => 'Inicio de Registro',
    'registration_end' => 'Fin de Registro',
    'campaign_start' => 'Inicio de Campaña',
    'campaign_end' => 'Fin de Campaña',

    // Estado del evento
    'status' => 'Estado',
    'planned' => 'Planificado',
    'registration_open' => 'Registro Abierto',
    'campaign_period' => 'Período de Campaña',
    'voting_day' => 'Día de Votación',
    'counting' => 'Conteo',
    'completed' => 'Completado',
    'cancelled' => 'Cancelado',
    'suspended' => 'Suspendido',

    // Configuración electoral
    'electoral_config' => 'Configuración Electoral',
    'voting_system' => 'Sistema de Votación',
    'ballot_type' => 'Tipo de Boleta',
    'voting_hours' => 'Horario de Votación',
    'start_time' => 'Hora de Inicio',
    'end_time' => 'Hora de Fin',
    'eligible_voters' => 'Votantes Habilitados',
    'total_centers' => 'Total de Centros',
    'total_tables' => 'Total de Mesas',

    // Candidatos y partidos
    'candidates' => 'Candidatos',
    'parties' => 'Partidos',
    'candidate_name' => 'Nombre del Candidato',
    'party_name' => 'Nombre del Partido',
    'coalition' => 'Coalición',
    'ballot_position' => 'Posición en Boleta',
    'add_candidate' => 'Agregar Candidato',
    'edit_candidate' => 'Editar Candidato',
    'remove_candidate' => 'Remover Candidato',

    // Resultados
    'results' => 'Resultados',
    'preliminary_results' => 'Resultados Preliminares',
    'final_results' => 'Resultados Finales',
    'votes_count' => 'Conteo de Votos',
    'total_votes' => 'Total de Votos',
    'valid_votes' => 'Votos Válidos',
    'invalid_votes' => 'Votos Inválidos',
    'blank_votes' => 'Votos en Blanco',
    'null_votes' => 'Votos Nulos',
    'participation' => 'Participación',
    'abstention' => 'Abstención',

    // Ubicación geográfica
    'geographic_scope' => 'Ámbito Geográfico',
    'national' => 'Nacional',
    'state_level' => 'Estadal',
    'municipal_level' => 'Municipal',
    'parish_level' => 'Parroquial',
    'affected_states' => 'Estados Afectados',
    'affected_municipalities' => 'Municipios Afectados',
    'affected_parishes' => 'Parroquias Afectadas',

    // Acciones
    'save' => 'Guardar',
    'update' => 'Actualizar',
    'cancel' => 'Cancelar',
    'delete' => 'Eliminar',
    'view_details' => 'Ver Detalles',
    'edit' => 'Editar',
    'publish' => 'Publicar',
    'activate' => 'Activar',
    'deactivate' => 'Desactivar',
    'export' => 'Exportar',
    'import' => 'Importar',
    'generate_report' => 'Generar Reporte',

    // Filtros y búsqueda
    'search' => 'Buscar',
    'filter_by' => 'Filtrar por',
    'clear_filters' => 'Limpiar Filtros',
    'search_by_name' => 'Buscar por nombre o código',
    'filter_by_type' => 'Filtrar por tipo',
    'filter_by_status' => 'Filtrar por estado',
    'filter_by_date' => 'Filtrar por fecha',
    'filter_by_scope' => 'Filtrar por ámbito',
    'advanced_search' => 'Búsqueda Avanzada',

    // Estadísticas
    'statistics' => 'Estadísticas',
    'total_events' => 'Total de Eventos',
    'active_events' => 'Eventos Activos',
    'completed_events' => 'Eventos Completados',
    'upcoming_events' => 'Próximos Eventos',
    'events_by_type' => 'Eventos por Tipo',
    'events_by_year' => 'Eventos por Año',
    'average_participation' => 'Participación Promedio',

    // Mensajes
    'event_created' => 'Evento electoral creado exitosamente',
    'event_updated' => 'Evento electoral actualizado exitosamente',
    'event_deleted' => 'Evento electoral eliminado exitosamente',
    'candidate_added' => 'Candidato agregado exitosamente',
    'candidate_updated' => 'Candidato actualizado exitosamente',
    'candidate_removed' => 'Candidato removido exitosamente',
    'results_updated' => 'Resultados actualizados exitosamente',
    'error_creating_event' => 'Error al crear el evento electoral',
    'error_updating_event' => 'Error al actualizar el evento electoral',
    'error_deleting_event' => 'Error al eliminar el evento electoral',
    'event_code_exists' => 'Ya existe un evento con este código',
    'no_results' => 'No se encontraron eventos con los filtros aplicados',

    // Confirmaciones
    'confirm_delete' => 'Confirmar Eliminación',
    'confirm_delete_event' => '¿Está seguro de que desea eliminar este evento electoral?',
    'confirm_delete_candidate' => '¿Está seguro de que desea remover este candidato?',
    'action_irreversible' => 'Esta acción no se puede deshacer.',
    'event_has_results' => 'Este evento tiene resultados registrados.',
    'event_is_active' => 'Este evento está activo.',

    // Validaciones
    'event_name_required' => 'El nombre del evento es obligatorio',
    'event_code_required' => 'El código del evento es obligatorio',
    'event_type_required' => 'El tipo de evento es obligatorio',
    'election_date_required' => 'La fecha de elección es obligatoria',
    'election_date_future' => 'La fecha de elección debe ser futura',
    'registration_dates_valid' => 'Las fechas de registro deben ser válidas',
    'campaign_dates_valid' => 'Las fechas de campaña deben ser válidas',
    'voting_hours_valid' => 'El horario de votación debe ser válido',
    'candidate_name_required' => 'El nombre del candidato es obligatorio',
    'party_name_required' => 'El nombre del partido es obligatorio',

    // Exportación e importación
    'export_excel' => 'Exportar a Excel',
    'export_pdf' => 'Exportar a PDF',
    'export_csv' => 'Exportar a CSV',
    'import_events' => 'Importar Eventos',
    'import_candidates' => 'Importar Candidatos',
    'import_results' => 'Importar Resultados',
    'download_template' => 'Descargar Plantilla',
    'upload_file' => 'Subir Archivo',
    'file_formats' => 'Formatos permitidos: Excel (.xlsx), CSV (.csv)',
    'export_successful' => 'Exportación realizada exitosamente',
    'import_successful' => 'Importación realizada exitosamente',
    'import_errors' => 'Se encontraron errores en la importación',

    // Información adicional
    'notes' => 'Observaciones',
    'created_at' => 'Fecha de Creación',
    'updated_at' => 'Última Actualización',
    'created_by' => 'Creado por',
    'updated_by' => 'Actualizado por',
    'legal_framework' => 'Marco Legal',
    'regulations' => 'Reglamentación',
    'budget' => 'Presupuesto',
    'cost' => 'Costo',

    // Reportes
    'reports' => 'Reportes',
    'event_report' => 'Reporte de Evento',
    'candidates_report' => 'Reporte de Candidatos',
    'results_report' => 'Reporte de Resultados',
    'participation_report' => 'Reporte de Participación',
    'statistical_report' => 'Reporte Estadístico',

    // Permisos
    'view_electoral_events' => 'Ver Eventos Electorales',
    'create_electoral_events' => 'Crear Eventos Electorales',
    'update_electoral_events' => 'Actualizar Eventos Electorales',
    'delete_electoral_events' => 'Eliminar Eventos Electorales',
    'export_electoral_events' => 'Exportar Eventos Electorales',
    'manage_candidates' => 'Gestionar Candidatos',
    'manage_results' => 'Gestionar Resultados',
];

<?php

return [
    'title' => 'Captación de Votantes',
    'title_description' => 'Gestionar el proceso de captación de votantes mediante la estrategia 1x10',

    // Navigation and menus
    'listing' => 'Listado',
    'add_capture' => 'Agregar Captación',
    'view_capture' => 'Ver Captación',
    'edit_capture' => 'Editar Captación',
    'capture_stats' => 'Estadísticas de Captación',

    // Tabs and filters
    'all' => 'Todos',
    'pending' => 'Pendientes',
    'contacted' => 'Contactados',
    'confirmed' => 'Confirmados',
    'not_interested' => 'No Interesados',
    'unreachable' => 'No Localizables',
    'overdue' => 'Vencidos',
    'needs_follow_up' => 'Requieren Seguimiento',

    // Form fields - Personal data
    'personal_data' => 'Datos Personales',
    'first_name' => 'Nombres',
    'last_name' => 'Apellidos',
    'document_number' => 'Cédula de Identidad',
    'birth_date' => 'Fecha de Nacimiento',
    'gender' => '<PERSON><PERSON><PERSON>',
    'male' => 'Masculino',
    'female' => 'Femenino',
    'other' => 'Otro',

    // Contact information
    'contact_information' => 'Información de Contacto',
    'phone' => 'Teléfono',
    'secondary_phone' => 'Teléfono Secundario',
    'email' => 'Correo Electrónico',
    'address' => 'Dirección',

    // Location
    'location' => 'Ubicación',
    'state' => 'Estado',
    'municipality' => 'Municipio',
    'parish' => 'Parroquia',
    'select_state' => 'Seleccionar Estado',
    'select_municipality' => 'Seleccionar Municipio',
    'select_parish' => 'Seleccionar Parroquia',

    // Electoral information
    'electoral_information' => 'Información Electoral',
    'voting_center' => 'Centro de Votación',
    'voting_table' => 'Mesa de Votación',
    'select_voting_center' => 'Seleccionar Centro de Votación',

    // Capture management
    'capture_management' => 'Gestión de Captación',
    'responsible' => 'Responsable',
    'capture_status' => 'Estado de Captación',
    'capture_date' => 'Fecha de Captación',
    'last_contact_date' => 'Última Fecha de Contacto',
    'next_contact_date' => 'Próxima Fecha de Contacto',
    'contact_notes' => 'Notas de Contacto',
    'select_responsible' => 'Seleccionar Responsable',

    // Capture source and method
    'capture_details' => 'Detalles de Captación',
    'capture_source' => 'Fuente de Captación',
    'contact_method' => 'Método de Contacto',
    'contact_attempts' => 'Intentos de Contacto',

    // Capture sources
    'door_to_door' => 'Puerta a Puerta',
    'phone_call' => 'Llamada Telefónica',
    'social_media' => 'Redes Sociales',
    'event' => 'Evento',
    'referral' => 'Referido',
    'other_source' => 'Otro',

    // Contact methods
    'phone_contact' => 'Teléfono',
    'whatsapp' => 'WhatsApp',
    'visit' => 'Visita',
    'email_contact' => 'Correo Electrónico',
    'social_media_contact' => 'Redes Sociales',

    // Interest and commitment
    'interest_commitment' => 'Interés y Compromiso',
    'interest_level' => 'Nivel de Interés',
    'commitment_level' => 'Nivel de Compromiso',
    'willing_to_vote' => 'Dispuesto a Votar',
    'willing_to_mobilize' => 'Dispuesto a Movilizar',

    // Interest levels
    'high_interest' => 'Alto',
    'medium_interest' => 'Medio',
    'low_interest' => 'Bajo',
    'no_interest' => 'Ninguno',

    // Commitment levels
    'committed' => 'Comprometido',
    'likely' => 'Probable',
    'uncertain' => 'Incierto',
    'unlikely' => 'Improbable',

    // Follow-up
    'follow_up' => 'Seguimiento',
    'needs_follow_up_field' => 'Requiere Seguimiento',
    'follow_up_date' => 'Fecha de Seguimiento',
    'reminder_sent' => 'Recordatorio Enviado',
    'last_reminder_sent' => 'Último Recordatorio Enviado',

    // Conversion
    'conversion' => 'Conversión',
    'converted_to_person' => 'Convertido a Persona',
    'conversion_date' => 'Fecha de Conversión',
    'convert_to_person' => 'Convertir a Persona',

    // Status
    'status' => 'Estado',
    'active' => 'Activo',
    'inactive' => 'Inactivo',
    'archived' => 'Archivado',
    'notes' => 'Notas',

    // Actions
    'actions' => 'Acciones',
    'view' => 'Ver',
    'edit' => 'Editar',
    'delete' => 'Eliminar',
    'mark_as_contacted' => 'Marcar como Contactado',
    'mark_as_confirmed' => 'Marcar como Confirmado',
    'schedule_follow_up' => 'Programar Seguimiento',
    'send_reminder' => 'Enviar Recordatorio',

    // Statistics
    'total_captures' => 'Total de Captaciones',
    'pending_captures' => 'Captaciones Pendientes',
    'confirmed_captures' => 'Captaciones Confirmadas',
    'conversion_rate' => 'Tasa de Conversión',
    'overdue_captures' => 'Captaciones Vencidas',
    'captures_this_month' => 'Captaciones este Mes',
    'captures_by_responsible' => 'Captaciones por Responsable',
    'captures_by_status' => 'Captaciones por Estado',

    // Messages
    'capture_created' => 'Captación creada exitosamente',
    'capture_updated' => 'Captación actualizada exitosamente',
    'capture_deleted' => 'Captación eliminada exitosamente',
    'capture_contacted' => 'Captación marcada como contactada',
    'capture_confirmed' => 'Captación confirmada exitosamente',
    'person_converted' => 'Captación convertida a persona exitosamente',
    'reminder_sent' => 'Recordatorio enviado exitosamente',

    // Errors
    'error_creating_capture' => 'Error al crear la captación',
    'error_updating_capture' => 'Error al actualizar la captación',
    'error_deleting_capture' => 'Error al eliminar la captación',
    'error_converting_person' => 'Error al convertir a persona',
    'document_already_exists' => 'Ya existe una captación con esta cédula',
    'responsible_not_found' => 'Responsable no encontrado',

    // Confirmations
    'confirm_delete' => '¿Estás seguro de que deseas eliminar esta captación?',
    'confirm_convert' => '¿Estás seguro de que deseas convertir esta captación a persona?',
    'confirm_mark_contacted' => '¿Marcar como contactado?',
    'confirm_mark_confirmed' => '¿Confirmar esta captación?',

    // Search and filters
    'search' => 'Buscar',
    'search_placeholder' => 'Buscar por nombre, cédula, teléfono...',
    'filter_by_status' => 'Filtrar por Estado',
    'filter_by_responsible' => 'Filtrar por Responsable',
    'filter_by_location' => 'Filtrar por Ubicación',
    'filter_by_date' => 'Filtrar por Fecha',
    'clear_filters' => 'Limpiar Filtros',

    // Dates
    'today' => 'Hoy',
    'yesterday' => 'Ayer',
    'this_week' => 'Esta Semana',
    'this_month' => 'Este Mes',
    'days_ago' => 'hace :days días',
    'days_from_now' => 'en :days días',

    // Validation
    'required_field' => 'Este campo es obligatorio',
    'invalid_document' => 'Número de cédula inválido',
    'invalid_phone' => 'Número de teléfono inválido',
    'invalid_email' => 'Correo electrónico inválido',
    'invalid_date' => 'Fecha inválida',

    // Permissions
    'view_captures' => 'Ver Captaciones',
    'create_captures' => 'Crear Captaciones',
    'update_captures' => 'Actualizar Captaciones',
    'delete_captures' => 'Eliminar Captaciones',
    'manage_captures' => 'Gestionar Captaciones',
    'view_capture_stats' => 'Ver Estadísticas de Captación',

    // Reminder notifications
    'reminder_subject' => 'Recordatorio de Captación de Votante',
    'reminder_greeting' => 'Hola :name,',
    'reminder_message' => 'Tienes una captación pendiente de seguimiento para :voter_name (C.I.: :document).',
    'reminder_footer' => 'Recuerda mantener el contacto regular con los votantes captados para asegurar su participación.',
    'reminder_database_message' => 'Captación de :voter_name requiere seguimiento',
    'no_contact_details' => 'Sin información de contacto disponible',
    'days_overdue' => ':days días de retraso',
];

<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function (): void {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

// Schedule capture reminders
Schedule::command('captures:send-reminders')
    ->dailyAt(config('voter_captures.reminders.send_time', '09:00'))
    ->when(function () {
        return config('voter_captures.reminders.enabled', true);
    });

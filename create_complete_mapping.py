import pandas as pd
import json

def analyze_excel_vs_seeder():
    """Analyze Excel data vs UbicacionesSeeder to create complete mapping"""
    print("🔍 Analizando datos del Excel vs UbicacionesSeeder...")
    
    # Read Excel data
    df = pd.read_excel('database/seeders/data/centros.xlsx')
    df_clean = df.iloc[:, :13].copy()
    
    # Filter out EXTERIOR
    df_clean = df_clean[df_clean['estado_corto'] != 'EXTERIOR']
    
    print(f"📊 Total records: {len(df_clean)}")
    
    # Get unique values from Excel
    excel_states = set(df_clean['estado_corto'].dropna().unique())
    excel_municipalities = set()
    excel_parishes = set()
    
    for _, row in df_clean.iterrows():
        if pd.notna(row['municipio_corto']):
            excel_municipalities.add(f"{row['estado_corto']}|{row['municipio_corto']}")
        if pd.notna(row['parroquia_corto']):
            excel_parishes.add(f"{row['estado_corto']}|{row['municipio_corto']}|{row['parroquia_corto']}")
    
    print(f"📍 Estados en Excel: {len(excel_states)}")
    print(f"🏛️  Municipios en Excel: {len(excel_municipalities)}")
    print(f"🏘️  Parroquias en Excel: {len(excel_parishes)}")
    
    # Define UbicacionesSeeder data (extracted from the seeder)
    seeder_states = {
        'Amazonas', 'Anzoátegui', 'Apure', 'Aragua', 'Barinas', 'Bolívar', 
        'Carabobo', 'Cojedes', 'Delta Amacuro', 'Distrito Capital', 'Falcón', 
        'Guárico', 'La Guaira', 'Lara', 'Mérida', 'Miranda', 'Monagas', 
        'Nueva Esparta', 'Portuguesa', 'Sucre', 'Táchira', 'Trujillo', 
        'Yaracuy', 'Zulia', 'Dependencias Federales'
    }
    
    # Create state mapping
    state_mapping = {
        'AMAZONAS': 'Amazonas',
        'ANZOATEGUI': 'Anzoátegui',
        'APURE': 'Apure',
        'ARAGUA': 'Aragua',
        'BARINAS': 'Barinas',
        'BOLIVAR': 'Bolívar',
        'CARABOBO': 'Carabobo',
        'COJEDES': 'Cojedes',
        'DELTA AMACURO': 'Delta Amacuro',
        'CAPITAL': 'Distrito Capital',
        'FALCON': 'Falcón',
        'GUARICO': 'Guárico',
        'LA GUAIRA': 'La Guaira',
        'LARA': 'Lara',
        'MERIDA': 'Mérida',
        'MIRANDA': 'Miranda',
        'MONAGAS': 'Monagas',
        'NUEVA ESPARTA': 'Nueva Esparta',
        'PORTUGUESA': 'Portuguesa',
        'SUCRE': 'Sucre',
        'TACHIRA': 'Táchira',
        'TRUJILLO': 'Trujillo',
        'YARACUY': 'Yaracuy',
        'ZULIA': 'Zulia',
        'DEPENDENCIAS FEDERALES': 'Dependencias Federales'
    }
    
    # Analyze unmapped states
    unmapped_states = excel_states - set(state_mapping.keys())
    print(f"\n❌ Estados no mapeados: {unmapped_states}")
    
    # Create municipality mapping based on UbicacionesSeeder
    municipality_mapping = {}
    
    # Amazonas municipalities
    amazonas_municipalities = {
        'ALTO ORINOCO': 'Alto Orinoco',
        'ATABAPO': 'Atabapo', 
        'ATURES': 'Atures',
        'AUTANA': 'Autana',
        'MANAPIARE': 'Manapiare',
        'MAROA': 'Maroa',
        'RIO NEGRO': 'Río Negro'
    }
    
    for excel_name, seeder_name in amazonas_municipalities.items():
        municipality_mapping[f'Amazonas|{excel_name}'] = f'Amazonas|{seeder_name}'
    
    # Anzoátegui municipalities
    anzoategui_municipalities = {
        'ANACO': 'Anaco',
        'ARAGUA': 'Aragua',
        'BOLIVAR': 'Bolívar',
        'BRUZUAL': 'Bruzual',
        'CAJIGAL': 'Cajigal',
        'CARVAJAL': 'Carvajal',
        'FREITES': 'Freites',
        'GUANIPA': 'Guanipa',
        'GUANTA': 'Guanta',
        'INDEPENDENCIA': 'Independencia',
        'LIBERTAD': 'Libertad',
        'MCGREGOR': 'Sir Arthur McGregor',
        'MIRANDA': 'Miranda',
        'MONAGAS': 'Monagas',
        'PEÑALVER': 'Peñalver',
        'PIRITU': 'Píritu',
        'SAN JUAN DE CAPISTRANO': 'San Juan de Capistrano',
        'SANTA ANA': 'Santa Ana',
        'SIMON BOLIVAR': 'Simón Bolívar',
        'SIMON RODRIGUEZ': 'Simón Rodríguez',
        'SOTILLO': 'Sotillo',
        'TURISMO': 'Turismo',
        'URBANEJA': 'Urbaneja'
    }
    
    for excel_name, seeder_name in anzoategui_municipalities.items():
        municipality_mapping[f'Anzoátegui|{excel_name}'] = f'Anzoátegui|{seeder_name}'
    
    # Create parish mapping based on ACTUAL UbicacionesSeeder data
    parish_mapping = {}

    # IMPORTANT: Map Excel parishes to what's ACTUALLY in UbicacionesSeeder
    # Many Excel parishes don't exist in the seeder, so we need to map them correctly

    # Amazonas parishes (based on actual seeder)
    amazonas_parishes = {
        # Alto Orinoco - These match the seeder
        'Amazonas|Alto Orinoco|LA ESMERALDA': 'Amazonas|Alto Orinoco|La Esmeralda',
        'Amazonas|Alto Orinoco|HUACHAMACARE': 'Amazonas|Alto Orinoco|Huachamacare',
        'Amazonas|Alto Orinoco|MARAWAKA': 'Amazonas|Alto Orinoco|Marawaka',
        'Amazonas|Alto Orinoco|MAVACA': 'Amazonas|Alto Orinoco|Mavaca',
        'Amazonas|Alto Orinoco|SIERRA PARIMA': 'Amazonas|Alto Orinoco|Sierra Parima',

        # Atabapo - PROBLEM: Excel parishes don't match seeder!
        # Seeder has: Fernando Girón Tovar, Luis Alberto Gómez, Pahueña, Platanillal
        # Excel has: CANAME, SAN FERNANDO DE ATABAPO, UCATA, YAPACANA
        # We need to add these to the seeder or map them to existing ones

        # Atures - These match the seeder
        'Amazonas|Atures|FERNANDO GIRON TOVAR': 'Amazonas|Atures|Fernando Girón Tovar',
        'Amazonas|Atures|LUIS ALBERTO GOMEZ': 'Amazonas|Atures|Luis Alberto Gómez',
        'Amazonas|Atures|PARHUEÑA': 'Amazonas|Atures|Pahueña',
        'Amazonas|Atures|PLATANILLAL': 'Amazonas|Atures|Platanillal',

        # Autana - PROBLEM: Excel has Isla de Ratón but seeder doesn't
        # Seeder has: Samariapo, Sipapo, Munduapo, Guayapo
        'Amazonas|Autana|SAMARIAPO': 'Amazonas|Autana|Samariapo',
        'Amazonas|Autana|SIPAPO': 'Amazonas|Autana|Sipapo',
        'Amazonas|Autana|MUNDUAPO': 'Amazonas|Autana|Munduapo',
        'Amazonas|Autana|GUAYAPO': 'Amazonas|Autana|Guayapo',

        # Manapiare - PROBLEM: Excel parishes don't match seeder!
        # Seeder has: Alto Ventuari, Medio Ventuari, Bajo Ventuari
        # Excel has: SAN JUAN DE MANAPIARE, ALTO VENTUARI, MEDIO VENTUARI, BAJO VENTUARI
        'Amazonas|Manapiare|ALTO VENTUARI': 'Amazonas|Manapiare|Alto Ventuari',
        'Amazonas|Manapiare|MEDIO VENTUARI': 'Amazonas|Manapiare|Medio Ventuari',
        'Amazonas|Manapiare|BAJO VENTUARI': 'Amazonas|Manapiare|Bajo Ventuari',

        # Maroa - PROBLEM: Excel parishes don't match seeder!
        # Seeder has: Victorino, Comunidad
        # Excel has: MAROA, VICTORINO, COMUNIDAD
        'Amazonas|Maroa|VICTORINO': 'Amazonas|Maroa|Victorino',
        'Amazonas|Maroa|COMUNIDAD': 'Amazonas|Maroa|Comunidad',

        # Río Negro - These mostly match
        'Amazonas|Río Negro|SOLANO': 'Amazonas|Río Negro|Solano',
        'Amazonas|Río Negro|CASIQUIARE': 'Amazonas|Río Negro|Casiquiare',
        'Amazonas|Río Negro|COCUY': 'Amazonas|Río Negro|Cocuy',
        'Amazonas|Río Negro|SAN CARLOS DE RIO NEGRO': 'Amazonas|Río Negro|San Carlos de Río Negro',
    }

    # Anzoátegui parishes (based on actual seeder)
    anzoategui_parishes = {
        # Anaco - These match the seeder
        'Anzoátegui|Anaco|ANACO': 'Anzoátegui|Anaco|Anaco',
        'Anzoátegui|Anaco|SAN JOAQUIN': 'Anzoátegui|Anaco|San Joaquín',

        # Aragua - These match the seeder
        'Anzoátegui|Aragua|ARAGUA DE BARCELONA': 'Anzoátegui|Aragua|Aragua de Barcelona',
        'Anzoátegui|Aragua|CACHIPO': 'Anzoátegui|Aragua|Cachipo',

        # Bolívar - These match the seeder
        'Anzoátegui|Bolívar|EL CARMEN': 'Anzoátegui|Bolívar|El Carmen',
        'Anzoátegui|Bolívar|SAN CRISTOBAL': 'Anzoátegui|Bolívar|San Cristóbal',
        'Anzoátegui|Bolívar|BERGANTIN': 'Anzoátegui|Bolívar|Bergantín',
        'Anzoátegui|Bolívar|CAIGUA': 'Anzoátegui|Bolívar|Caigua',
        'Anzoátegui|Bolívar|EL PILAR': 'Anzoátegui|Bolívar|El Pilar',
        'Anzoátegui|Bolívar|NARICUAL': 'Anzoátegui|Bolívar|Naricual',
    }

    # Zulia parishes (based on actual seeder)
    zulia_parishes = {
        # Miranda - These match the seeder
        'Zulia|Miranda|ALTAGRACIA': 'Zulia|Miranda|Altagracia',
        'Zulia|Miranda|ANA MARIA CAMPOS': 'Zulia|Miranda|Ana María Campos',
        'Zulia|Miranda|FARIA': 'Zulia|Miranda|Faría',
        'Zulia|Miranda|SAN ANTONIO': 'Zulia|Miranda|San Antonio',
        'Zulia|Miranda|SAN JOSE': 'Zulia|Miranda|San José',

        # Rosario de Perijá - These match the seeder
        'Zulia|Rosario de Perijá|EL ROSARIO': 'Zulia|Rosario de Perijá|El Rosario',
        'Zulia|Rosario de Perijá|DONALDO GARCIA': 'Zulia|Rosario de Perijá|Donaldo García',
        'Zulia|Rosario de Perijá|SIXTO ZAMBRANO': 'Zulia|Rosario de Perijá|Sixto Zambrano',

        # San Francisco - These match the seeder
        'Zulia|San Francisco|SAN FRANCISCO': 'Zulia|San Francisco|San Francisco',
        'Zulia|San Francisco|EL BAJO': 'Zulia|San Francisco|El Bajo',
        'Zulia|San Francisco|DOMITILA FLORES': 'Zulia|San Francisco|Domitila Flores',
        'Zulia|San Francisco|FRANCISCO OCHOA': 'Zulia|San Francisco|Francisco Ochoa',
        'Zulia|San Francisco|LOS CORTIJOS': 'Zulia|San Francisco|Los Cortijos',
        'Zulia|San Francisco|MARCIAL HERNANDEZ': 'Zulia|San Francisco|Marcial Hernández',
        'Zulia|San Francisco|JOSE DOMINGO RUS': 'Zulia|San Francisco|José Domingo Rus',

        # Santa Rita - These match the seeder
        'Zulia|Santa Rita|SANTA RITA': 'Zulia|Santa Rita|Santa Rita',
        'Zulia|Santa Rita|EL MENE': 'Zulia|Santa Rita|El Mene',
        'Zulia|Santa Rita|JOSE CENOVIO URRIBARRI': 'Zulia|Santa Rita|José Cenobio Urribarrí',
        'Zulia|Santa Rita|PEDRO LUCAS URRIBARRI': 'Zulia|Santa Rita|Pedro Lucas Urribarrí',

        # Sucre - These match the seeder
        'Zulia|Sucre|BOBURES': 'Zulia|Sucre|Bobures',
        'Zulia|Sucre|EL BATEY': 'Zulia|Sucre|El Batey',
        'Zulia|Sucre|GIBRALTAR': 'Zulia|Sucre|Gibraltar',
        'Zulia|Sucre|HERAS': 'Zulia|Sucre|Heras',
        'Zulia|Sucre|M.ARTURO CELESTINO A': 'Zulia|Sucre|Monseñor Arturo Álvarez',
        'Zulia|Sucre|ROMULO GALLEGOS': 'Zulia|Sucre|Rómulo Gallegos',

        # Valmore Rodríguez - These match the seeder
        'Zulia|Valmore Rodríguez|RAFAEL URDANETA': 'Zulia|Valmore Rodríguez|Rafael Urdaneta',
        'Zulia|Valmore Rodríguez|LA VICTORIA': 'Zulia|Valmore Rodríguez|La Victoria',
        'Zulia|Valmore Rodríguez|RAUL CUENCA': 'Zulia|Valmore Rodríguez|Raúl Cuenca',
    }

    parish_mapping.update(amazonas_parishes)
    parish_mapping.update(anzoategui_parishes)
    parish_mapping.update(zulia_parishes)
    
    return state_mapping, municipality_mapping, parish_mapping

def save_mappings(state_mapping, municipality_mapping, parish_mapping):
    """Save mappings to JSON files for reference"""
    
    with open('state_mapping.json', 'w', encoding='utf-8') as f:
        json.dump(state_mapping, f, ensure_ascii=False, indent=2)
    
    with open('municipality_mapping.json', 'w', encoding='utf-8') as f:
        json.dump(municipality_mapping, f, ensure_ascii=False, indent=2)
    
    with open('parish_mapping.json', 'w', encoding='utf-8') as f:
        json.dump(parish_mapping, f, ensure_ascii=False, indent=2)
    
    print("💾 Mapeos guardados en archivos JSON")

if __name__ == "__main__":
    state_mapping, municipality_mapping, parish_mapping = analyze_excel_vs_seeder()
    save_mappings(state_mapping, municipality_mapping, parish_mapping)
    
    print(f"\n📋 RESUMEN DE MAPEOS:")
    print(f"   Estados: {len(state_mapping)}")
    print(f"   Municipios: {len(municipality_mapping)}")
    print(f"   Parroquias: {len(parish_mapping)}")

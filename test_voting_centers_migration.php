<?php

/**
 * Script de prueba para verificar las migraciones de centros de votación
 * 
 * Este script verifica:
 * 1. Que las migraciones se ejecuten correctamente
 * 2. Que la estructura de la tabla sea la correcta
 * 3. Que el seeder funcione con algunos registros de prueba
 */

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

echo "🧪 INICIANDO PRUEBAS DE MIGRACIÓN DE CENTROS DE VOTACIÓN\n";
echo "=" . str_repeat("=", 60) . "\n\n";

try {
    // 1. Verificar que la tabla existe
    echo "1️⃣  Verificando que la tabla 'voting_centers' existe...\n";
    if (Schema::hasTable('voting_centers')) {
        echo "   ✅ Tabla 'voting_centers' existe\n\n";
    } else {
        echo "   ❌ Tabla 'voting_centers' NO existe\n";
        echo "   💡 Ejecuta: php artisan migrate\n\n";
        exit(1);
    }

    // 2. Verificar estructura de la tabla
    echo "2️⃣  Verificando estructura de la tabla...\n";
    $expectedColumns = [
        'id', 'code', 'old_code', 'name', 'address',
        'state_id', 'municipality_id', 'parish_id',
        'total_voters', 'total_tables', 'status',
        'latitude', 'longitude', 'additional_info', 'active',
        'created_at', 'updated_at'
    ];

    $missingColumns = [];
    foreach ($expectedColumns as $column) {
        if (!Schema::hasColumn('voting_centers', $column)) {
            $missingColumns[] = $column;
        }
    }

    if (empty($missingColumns)) {
        echo "   ✅ Todas las columnas requeridas están presentes\n\n";
    } else {
        echo "   ❌ Faltan columnas: " . implode(', ', $missingColumns) . "\n\n";
        exit(1);
    }

    // 3. Verificar índices
    echo "3️⃣  Verificando índices...\n";
    $indexes = DB::select("SHOW INDEX FROM voting_centers");
    $indexNames = array_column($indexes, 'Key_name');
    
    $expectedIndexes = [
        'voting_centers_location_index',
        'voting_centers_status_index', 
        'voting_centers_code_index'
    ];

    $missingIndexes = [];
    foreach ($expectedIndexes as $index) {
        if (!in_array($index, $indexNames)) {
            $missingIndexes[] = $index;
        }
    }

    if (empty($missingIndexes)) {
        echo "   ✅ Todos los índices están presentes\n\n";
    } else {
        echo "   ⚠️  Faltan índices: " . implode(', ', $missingIndexes) . "\n";
        echo "   💡 Los índices son opcionales pero recomendados para rendimiento\n\n";
    }

    // 4. Verificar foreign keys
    echo "4️⃣  Verificando foreign keys...\n";
    $foreignKeys = DB::select("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_NAME = 'voting_centers' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");

    $expectedForeignKeys = ['state_id', 'municipality_id', 'parish_id'];
    $existingForeignKeys = array_column($foreignKeys, 'COLUMN_NAME');

    $missingForeignKeys = array_diff($expectedForeignKeys, $existingForeignKeys);

    if (empty($missingForeignKeys)) {
        echo "   ✅ Todas las foreign keys están configuradas\n\n";
    } else {
        echo "   ❌ Faltan foreign keys: " . implode(', ', $missingForeignKeys) . "\n\n";
        exit(1);
    }

    // 5. Verificar que las tablas relacionadas existen
    echo "5️⃣  Verificando tablas relacionadas...\n";
    $relatedTables = ['states', 'municipalities', 'parishes'];
    $missingTables = [];

    foreach ($relatedTables as $table) {
        if (!Schema::hasTable($table)) {
            $missingTables[] = $table;
        }
    }

    if (empty($missingTables)) {
        echo "   ✅ Todas las tablas relacionadas existen\n\n";
    } else {
        echo "   ❌ Faltan tablas: " . implode(', ', $missingTables) . "\n";
        echo "   💡 Ejecuta: php artisan db:seed --class=UbicacionesSeeder\n\n";
        exit(1);
    }

    // 6. Verificar que hay datos en las tablas relacionadas
    echo "6️⃣  Verificando datos en tablas relacionadas...\n";
    $statesCount = DB::table('states')->count();
    $municipalitiesCount = DB::table('municipalities')->count();
    $parishesCount = DB::table('parishes')->count();

    echo "   📊 Estados: {$statesCount}\n";
    echo "   📊 Municipios: {$municipalitiesCount}\n";
    echo "   📊 Parroquias: {$parishesCount}\n";

    if ($statesCount > 0 && $municipalitiesCount > 0 && $parishesCount > 0) {
        echo "   ✅ Hay datos en todas las tablas relacionadas\n\n";
    } else {
        echo "   ⚠️  Algunas tablas están vacías\n";
        echo "   💡 Ejecuta: php artisan db:seed --class=UbicacionesSeeder\n\n";
    }

    // 7. Verificar archivo CSV
    echo "7️⃣  Verificando archivo CSV...\n";
    $csvFile = 'database/seeders/data/voting_centers.csv';
    
    if (file_exists($csvFile)) {
        $lines = count(file($csvFile));
        echo "   ✅ Archivo CSV existe con {$lines} líneas\n\n";
    } else {
        echo "   ❌ Archivo CSV no encontrado: {$csvFile}\n\n";
        exit(1);
    }

    // 8. Prueba de inserción
    echo "8️⃣  Realizando prueba de inserción...\n";
    
    // Obtener una ubicación válida
    $state = DB::table('states')->first();
    $municipality = DB::table('municipalities')->where('state_id', $state->id)->first();
    $parish = DB::table('parishes')->where('municipality_id', $municipality->id)->first();

    if ($state && $municipality && $parish) {
        $testData = [
            'code' => 'TEST001',
            'name' => 'Centro de Prueba',
            'address' => 'Dirección de Prueba',
            'state_id' => $state->id,
            'municipality_id' => $municipality->id,
            'parish_id' => $parish->id,
            'total_voters' => 100,
            'total_tables' => 1,
            'status' => 'active',
            'active' => true,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        try {
            $id = DB::table('voting_centers')->insertGetId($testData);
            echo "   ✅ Inserción exitosa (ID: {$id})\n";
            
            // Limpiar el registro de prueba
            DB::table('voting_centers')->where('id', $id)->delete();
            echo "   🧹 Registro de prueba eliminado\n\n";
        } catch (Exception $e) {
            echo "   ❌ Error en inserción: " . $e->getMessage() . "\n\n";
            exit(1);
        }
    } else {
        echo "   ⚠️  No se pudo realizar la prueba de inserción (faltan datos de ubicación)\n\n";
    }

    echo "🎉 TODAS LAS PRUEBAS PASARON EXITOSAMENTE\n";
    echo "=" . str_repeat("=", 60) . "\n";
    echo "✅ La migración de centros de votación está lista\n";
    echo "💡 Puedes ejecutar: php artisan db:seed --class=VotingCentersSeeder\n\n";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 Archivo: " . $e->getFile() . "\n";
    echo "📍 Línea: " . $e->getLine() . "\n\n";
    exit(1);
}
